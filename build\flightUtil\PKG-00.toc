('D:\\work\\LEARNPY\\CODE\\pythonProject\\build\\flightUtil\\flightUtil.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\build\\flightUtil\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\build\\flightUtil\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\build\\flightUtil\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\build\\flightUtil\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\build\\flightUtil\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\build\\flightUtil\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('flightUtil',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\flightUtil.py',
   'PYSOURCE'),
  ('playwright\\driver\\package\\bin\\PrintDeps.exe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\PrintDeps.exe',
   'BINARY'),
  ('playwright\\driver\\node.exe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\node.exe',
   'BINARY'),
  ('python38.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\python38.dll',
   'BINARY'),
  ('_lzma.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_openssl.pyd',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_openssl.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp38-win_amd64.pyd',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\_cffi_backend.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp38-win_amd64.pyd',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\greenlet\\_greenlet.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libffi-7.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('python3.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\python3.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\Windows\\system32\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('base_library.zip',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\build\\flightUtil\\base_library.zip',
   'DATA'),
  ('cryptography-37.0.2.dist-info\\LICENSE.PSF',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography-37.0.2.dist-info\\LICENSE.PSF',
   'DATA'),
  ('cryptography-37.0.2.dist-info\\top_level.txt',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography-37.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('cryptography-37.0.2.dist-info\\WHEEL',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography-37.0.2.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-37.0.2.dist-info\\LICENSE.APACHE',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography-37.0.2.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-37.0.2.dist-info\\METADATA',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography-37.0.2.dist-info\\METADATA',
   'DATA'),
  ('cryptography-37.0.2.dist-info\\INSTALLER',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography-37.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-37.0.2.dist-info\\LICENSE.BSD',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography-37.0.2.dist-info\\LICENSE.BSD',
   'DATA'),
  ('cryptography-37.0.2.dist-info\\RECORD',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography-37.0.2.dist-info\\RECORD',
   'DATA'),
  ('cryptography-37.0.2.dist-info\\LICENSE',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography-37.0.2.dist-info\\LICENSE',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\oopDownloadBrowserMain.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\oopDownloadBrowserMain.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\android.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\android.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\socksClientCertificatesInterceptor.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\socksClientCertificatesInterceptor.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\types.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\nativeDeps.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\nativeDeps.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browserType.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\browserType.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\test\\inMemorySnapshotter.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\test\\inMemorySnapshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\browserFetcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\browserFetcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\fetch.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\fetch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fetch.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\fetch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkPage.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.DloKQa-h.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.DloKQa-h.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\jsHandle.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\jsHandle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-BW-aOBcL.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-BW-aOBcL.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\codicon.DCmgc-ay.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\codicon.DCmgc-ay.ttf',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crPage.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crPage.js',
   'DATA'),
  ('playwright\\driver\\package\\types\\types.d.ts',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\types\\types.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\webSocketMockSource.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\webSocketMockSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\deviceDescriptorsSource.json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\deviceDescriptorsSource.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\recorderUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\recorderUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\electron\\loader.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\electron\\loader.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\wsServer.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\wsServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\worker.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\worker.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\browser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\ascii.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\ascii.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\api.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\api.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\crypto.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\crypto.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\electronDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\electronDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\validatorPrimitives.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\validatorPrimitives.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\coverage.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\coverage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\java.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\java.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\testServerConnection-DeE2kSzz.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\testServerConnection-DeE2kSzz.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\dependencies.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\dependencies.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\embedded.Do_J5Hgs.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\embedded.Do_J5Hgs.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\channelOwner.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\channelOwner.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\index.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\download.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\download.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\javascript.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\javascript.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\zones.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\zones.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\stackTrace.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\stackTrace.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\stream.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\stream.js',
   'DATA'),
  ('playwright\\driver\\package\\index.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\common\\timeoutSettings.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\common\\timeoutSettings.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-BcaUAUCW.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-BcaUAUCW.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\accessibility.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\accessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiSerializer.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiSerializer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\outofprocess.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\outofprocess.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\consoleMessage.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\consoleMessage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\xtermModule.DSXBckUd.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\xtermModule.DSXBckUd.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\pipeTransport.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\pipeTransport.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\viewer\\traceViewer.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\viewer\\traceViewer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\languages.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\languages.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\network.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotter.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\debugControllerDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\debugControllerDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserTypeDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserTypeDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiPage.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiInput.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\localUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\localUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\protocol.yml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\protocol.yml',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_mac.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\electron\\electron.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\electron\\electron.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.B21BXreT.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.B21BXreT.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dialog.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dialog.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\workbench.D3JVcA9K.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\workbench.D3JVcA9K.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffConnection.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\spawnAsync.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\spawnAsync.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\artifact.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\artifact.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\codeMirrorModule.ez37Vkbh.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\codeMirrorModule.ez37Vkbh.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\dialogDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\dialogDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\transport.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\transport.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\validator.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\validator.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\launchApp.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\launchApp.js',
   'DATA'),
  ('playwright\\driver\\package\\cli.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\cli.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\connection.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\connection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\timeoutRunner.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\timeoutRunner.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.CrbWWHbf.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.CrbWWHbf.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crBrowser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\browsers.json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\browsers.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browserContext.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\browserContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiKeyboard.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiKeyboard.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\htmlReport\\index.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\htmlReport\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkWorkers.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkWorkers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\playwright.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\playwright.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiConnection.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\frames.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\frames.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\defaultFontFamilies.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\defaultFontFamilies.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\language.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\language.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\playwrightDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\playwrightDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\task.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\task.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiPdf.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiPdf.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffPage.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderInTraceViewer.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderInTraceViewer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\progress.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\progress.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_mac.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\androidServerImpl.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\androidServerImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\android\\android.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\android\\android.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\urlMatch.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\urlMatch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\utilityScriptSource.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\utilityScriptSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\macEditingCommands.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\macEditingCommands.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\clock.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\clock.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiBrowser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\types.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\recorder.B_SY1GJM.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\recorder.B_SY1GJM.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\compare.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\compare.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\httpServer.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\httpServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\time.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\time.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crPdf.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crPdf.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\debugController.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\debugController.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.CAYqod-m.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.CAYqod-m.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\headers.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\headers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\formData.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\formData.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browserType.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\browserType.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_linux.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_win.ps1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\bin\\README.md',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\README.md',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_win.ps1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\pageDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\pageDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\driver.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\cli\\driver.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\env.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\env.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\embedded.w7WN2u1R.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\embedded.w7WN2u1R.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fileChooser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\fileChooser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\transport.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\transport.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crCoverage.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crCoverage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\artifactDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\artifactDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\zipBundle.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\zipBundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crServiceWorker.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crServiceWorker.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\writableStreamDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\writableStreamDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\harRouter.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\harRouter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crAccessibility.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\selectorsDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\selectorsDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\artifact.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\artifact.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crProtocolHelper.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crProtocolHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\traceUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\traceUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\webSocketRouteDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\webSocketRouteDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\manualPromise.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\manualPromise.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkConnection.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\userAgent.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\userAgent.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\locator.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\locator.js',
   'DATA'),
  ('playwright\\driver\\package\\index.mjs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\index.mjs',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\zipFile.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\zipFile.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\videoRecorder.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\videoRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\happy-eyeballs.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\happy-eyeballs.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkExecutionContext.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\elementHandle.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\elementHandle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\playwright-logo.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\playwright-logo.svg',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\inspectorTab-DTusvprx.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\inspectorTab-DTusvprx.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clientInstrumentation.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\clientInstrumentation.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crDevTools.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crDevTools.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\fileChooser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\fileChooser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\programWithTestStub.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\cli\\programWithTestStub.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\inspectorTab.DLjBDrQR.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\inspectorTab.DLjBDrQR.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\page.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\page.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\errors.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\errors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundleImpl\\xdg-open',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utilsBundleImpl\\xdg-open',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\multimap.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\multimap.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\javascript.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\javascript.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\waiter.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\waiter.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_win.ps1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\network.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\workbench-DIEjrm3Z.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\workbench-DIEjrm3Z.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\selectors.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\selectors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\console.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\console.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crDragDrop.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crDragDrop.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\cssParser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\cssParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\appIcon.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\appIcon.png',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\browser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\zipBundleImpl.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\zipBundleImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\selectors.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\selectors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\electron.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\electron.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\socksInterceptor.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\socksInterceptor.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\remote\\playwrightServer.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\remote\\playwrightServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderFrontend.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderFrontend.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\hostPlatform.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\hostPlatform.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkInterceptableRequest.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkInterceptableRequest.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\throttledFile.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\throttledFile.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\tracing.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\tracing.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\expectUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\expectUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiProtocol.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiProtocol.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clock.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\clock.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\network.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffExecutionContext.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\tracing.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\tracing.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\usKeyboardLayout.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\usKeyboardLayout.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\chromium.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\chromium.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browserContext.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\browserContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\cookieStore.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\cookieStore.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\jsHandleDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\jsHandleDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\injectedScriptSource.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\injectedScriptSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\rtti.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\rtti.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderCollection.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderCollection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\dispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\dispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\processLauncher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\processLauncher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codicon-DCmgc-ay.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codicon-DCmgc-ay.ttf',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\embedded.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\embedded.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\tracingDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\tracingDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffAccessibility.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crNetworkManager.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crInput.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\jsonl.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\jsonl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\input.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\input.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\debugLogger.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\debugLogger.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\inprocess.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\inprocess.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\cdpSession.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\cdpSession.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorGenerators.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorGenerators.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\common\\types.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\common\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\types.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-CR6kB851.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-CR6kB851.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\imageChannel.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\imageChannel.js',
   'DATA'),
  ('playwright\\driver\\package\\types\\structs.d.ts',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\types\\structs.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\pollingRecorderSource.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\pollingRecorderSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\androidDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\androidDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\streamDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\streamDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiChromium.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiChromium.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\cdpSessionDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\cdpSessionDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkAccessibility.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkProvisionalPage.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkProvisionalPage.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_win.ps1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\networkDispatchers.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\networkDispatchers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\selectorParser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\selectorParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\instrumentation.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\instrumentation.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiDeserializer.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiDeserializer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\program.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\cli\\program.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\common\\socksProxy.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\common\\socksProxy.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundleImpl\\index.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utilsBundleImpl\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\python.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\python.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\inProcessFactory.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\inProcessFactory.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\index.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\ThirdPartyNotices.txt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\ThirdPartyNotices.txt',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\errors.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\errors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\consoleApiSource.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\consoleApiSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\remote\\playwrightConnection.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\remote\\playwrightConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\playwright-logo.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\playwright-logo.svg',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotterInjected.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotterInjected.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crExecutionContext.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\sw.bundle.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\sw.bundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\csharp.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\csharp.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_mac.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\localUtilsDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\localUtilsDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\fileUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\fileUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\screenshotter.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\screenshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiFirefox.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiFirefox.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\stats.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\stats.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\protocolError.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\protocolError.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\har\\harTracer.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\har\\harTracer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\jsonPipeDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\jsonPipeDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\har\\harRecorder.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\har\\harRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\traceUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\traceUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\elementHandlerDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\elementHandlerDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_mac.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\input.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\input.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\chromiumSwitches.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\chromiumSwitches.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\frameSelectors.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\frameSelectors.js',
   'DATA'),
  ('playwright\\driver\\LICENSE',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\LICENSE',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-ez37Vkbh.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-ez37Vkbh.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiNetworkManager.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\clockSource.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\clockSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\codeMirrorModule-CZTtn9l8.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\codeMirrorModule-CZTtn9l8.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_linux.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\page.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\page.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\playwright.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\playwright.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiExecutionContext.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\profiler.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\profiler.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\isomorphic\\utilityScriptSerializers.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\isomorphic\\utilityScriptSerializers.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_linux.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\firefoxPrefs.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\firefoxPrefs.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\recorder.Bfh_9UGt.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\recorder.Bfh_9UGt.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\recorder.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\recorder.html',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_linux.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\index.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\helper.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\helper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\xtermModule-BeNbaIVa.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\xtermModule-BeNbaIVa.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\android\\backendAdb.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\android\\backendAdb.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\jsonPipe.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\jsonPipe.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffBrowser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffNetworkManager.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkBrowser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\cssTokenizer.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\cssTokenizer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crConnection.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\firefox.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\firefox.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dom.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dom.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\colorUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\colorUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\api.json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\api.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\semaphore.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\semaphore.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\eventEmitter.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\eventEmitter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\index.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\webkit.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\webkit.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\events.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\events.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\deviceDescriptors.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\deviceDescriptors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkInput.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkInput.js',
   'DATA'),
  ('playwright\\driver\\package\\README.md',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\README.md',
   'DATA'),
  ('playwright\\driver\\package\\package.json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\package.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderRunner.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderRunner.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\video.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\video.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clientHelper.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\clientHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\linuxUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\linuxUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\eventsHelper.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\eventsHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\index.d.ts',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\index.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderApp.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderApp.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_mac.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\bin\\install_media_pack.ps1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\install_media_pack.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\browserServerImpl.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\browserServerImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\comparators.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\comparators.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\third_party\\pixelmatch.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\third_party\\pixelmatch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\contextRecorder.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\contextRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\snapshot.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\snapshot.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\mimeType.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\mimeType.js',
   'DATA'),
  ('playwright\\driver\\README.md',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\README.md',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\frame.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\frame.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserContextDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserContextDispatcher.js',
   'DATA'),
  ('playwright\\py.typed',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\py.typed',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundle.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utilsBundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\debug.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\debug.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\webError.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\webError.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\accessibility.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\accessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiOverCdp.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiOverCdp.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\debugger.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\debugger.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\stringUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\stringUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_win.ps1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\download.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\download.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\dialog.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\dialog.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffInput.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffInput.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_linux.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fileUploadUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\fileUploadUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorParser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\frameDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\frameDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\serializers.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\serializers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\debug.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\debug.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\third_party\\diff_match_patch.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\third_party\\diff_match_patch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\writableStream.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\writableStream.js',
   'DATA'),
  ('playwright\\driver\\package\\types\\protocol.d.ts',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\types\\protocol.d.ts',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v100\\py.typed',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v100\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v85\\py.typed',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v85\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v101\\py.typed',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v101\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'DATA'),
  ('selenium\\webdriver\\remote\\findElements.js',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\findElements.js',
   'DATA'),
  ('selenium\\py.typed',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\remote\\getAttribute.js',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\getAttribute.js',
   'DATA'),
  ('selenium\\webdriver\\remote\\isDisplayed.js',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\isDisplayed.js',
   'DATA'),
  ('selenium\\webdriver\\common\\mutation-listener.js',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\mutation-listener.js',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v102\\py.typed',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v102\\py.typed',
   'DATA')],
 'python38.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
