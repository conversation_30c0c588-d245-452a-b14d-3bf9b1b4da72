# import mysql.connector
# from mysql.connector.locales.eng import client_error
import pymysql
from utils.DbUtil import DBUtil


# mydb = mysql.connector.connect(
#     host="**************",  # 数据库主机地址
#     user="toc_test",  # 数据库用户名
#     passwd="toc_test@1644",  # 数据库密码
#     port="8077",
#     database="cdev"
# )
# mydb=pymysql.connect(
#     host="**************",  # 数据库主机地址
#     user="toc_test",  # 数据库用户名
#     passwd="toc_test@1644",  # 数据库密码
#     port=8077,
#     database="cdev"
# )


def searchdata(parameter, num):
    sqldict = {1: "select pay_no,need_pay_amount from tv2_pay_bill where order_no={} and pay_status=1",
               2: "select id,content from flight_sms where foid={}",
               3: 'DELETE from veryzhun_user_coupon where uid= {}',
               4: 'batch_delete_user_data'  # 特殊标识，用于批量删除
               }
    
    if num == 4:
        # 批量删除用户机票数据
        batch_delete_user_data(parameter)
        return
    
    sql = sqldict[num].format(parameter)
    # mydb.ping(reconnect= True)
    if num == 3:
        myresult = DBUtil.del_db(sql, 'veryzhunapp')
    else:
        myresult = DBUtil.select_db(sql, 'cdev')

    if num == 1:
        paylist = myresult[0]
        return paylist
    elif num== 2:
        for i in myresult:
            print(i)

def update_order_time(order_no, days):
    """根据订单号更新相关表的时间字段"""
    # 将天数转换为秒数（支持小数）
    seconds_to_add = int(days * 24 * 60 * 60)

    # 定义需要更新的表和对应的时间字段
    update_configs = [
        {
            'table': 'tv2_segment',
            'fields': ['dep_time', 'arr_time', 'bj_dep_time', 'bj_arr_time', 'create_time', 'update_time'],
            'condition': 'order_no = {}'
        },
        {
            'table': 'na_order_detail',
            'fields': ['order_time', 'pay_time', 'cancel_time', 'ticket_time', 'create_time', 'update_time'],
            'condition': 'order_no = {}'
        },
        {
            'table': 'tv2_order_detail',
            'fields': ['order_time', 'pay_time', 'refund_time', 'cancel_time', 'ticket_time', 'hold_time', 'create_time', 'update_time', 'submit_supplier_time'],
            'condition': 'order_no = {}'
        },
        {
            'table': 'tv2_order',
            'fields': ['order_time', 'pay_time', 'cancel_time', 'delete_time', 'create_time', 'update_time'],
            'condition': 'order_no = {}'
        },
        {
            'table': 'tv2_insurance',
            'fields': ['isu_success_time', 'refund_success_time', 'dep_time'],
            'condition': 'order_no = {}'
        },
        {
            'table': 'tv2_pay_bill',
            'fields': ['pay_time', 'pay_deadline'],
            'condition': 'order_no = {}'
        }
    ]

    total_updated = 0

    try:
        print(f'开始更新订单 {order_no} 的时间字段...')
        print(f'时间调整: {"+" if days >= 0 else ""}{days} 天 ({seconds_to_add} 秒)\n')

        for config in update_configs:
            table_name = config['table']
            fields = config['fields']
            condition = config['condition'].format(order_no)

            try:
                # 先查询该表是否有对应订单的记录
                count_sql = f"SELECT COUNT(*) FROM {table_name} WHERE {condition}"
                count_result = DBUtil.select_db(count_sql, 'cdev')

                if count_result and count_result[0][0] > 0:
                    record_count = count_result[0][0]

                    # 构建更新SQL，只更新非空的时间字段
                    update_parts = []
                    for field in fields:
                        update_parts.append(f"{field} = CASE WHEN {field} IS NOT NULL AND {field} > 0 THEN {field} + {seconds_to_add} ELSE {field} END")

                    update_sql = f"UPDATE {table_name} SET {', '.join(update_parts)} WHERE {condition}"

                    # 执行更新
                    DBUtil.adm_db(update_sql, 'cdev')

                    total_updated += record_count
                    print(f'  ✓ {table_name}: 更新 {record_count} 条记录的时间字段')
                else:
                    print(f'  - {table_name}: 无相关订单数据')

            except Exception as e:
                print(f'  ✗ {table_name}: 更新失败 - {str(e)}')
                continue

        print(f'\n时间更新完成，共更新 {total_updated} 条记录')

    except Exception as e:
        print(f'更新过程中出现错误: {str(e)}')
        raise

def batch_delete_user_data(uid):
    """批量删除用户机票相关数据"""
    # 定义要删除的表和对应的uid字段名
    delete_tables = [
        # 按依赖关系排序，先删除子表再删除父表
        ('tv2_order_detail', 'uid'),
        ('tv2_order_extend', 'uid'), 
        ('tv2_order_deduction', 'uid'),
        ('tv2_pay_bill', 'uid'),
        ('tv2_order_pay_summary', 'uid'),
        ('tv2_pay_serial', 'uid'),
        ('tv2_pay_marking_order', 'uid'),
        ('tv2_package_details', 'uid'),
        ('tv2_package', 'uid'),
        ('tv2_insurance', 'uid'),
        ('tv2_segment', 'uid'),
        ('tv2_refund_order', 'uid'),
        ('tv2_supplier_order', 'uid'),
        ('tv2_order', 'uid'),
        ('tv2_grab_order', 'uid'),
        ('tv2_consult_order', 'uid'),
        ('sm_order_detail', 'uid'),
        ('sm_order', 'uid'),
        ('na_order_detail', 'uid'),
        ('na_supplier_order', 'uid'),
        ('blind_box_order', 'uid'),
        ('user_debt_records', 'uid'),
        ('issue', 'uid'),
    ]
    
    total_deleted = 0
    
    try:
        print(f'开始删除用户 {uid} 的机票数据...')
        
        for table_name, uid_field in delete_tables:
            try:
                # 先查询要删除的记录数
                count_sql = f"SELECT COUNT(*) FROM {table_name} WHERE {uid_field} = {uid}"
                count_result = DBUtil.select_db(count_sql, 'cdev')
                
                if count_result and count_result[0][0] > 0:
                    record_count = count_result[0][0]
                    
                    # 执行删除
                    delete_sql = f"DELETE FROM {table_name} WHERE {uid_field} = {uid}"
                    DBUtil.del_db(delete_sql, 'cdev')
                    
                    total_deleted += record_count
                    print(f'  ✓ {table_name}: 删除 {record_count} 条记录')
                else:
                    print(f'  - {table_name}: 无数据')
                    
            except Exception as e:
                print(f'  ✗ {table_name}: 删除失败 - {str(e)}')
                continue
                
        print(f'\n删除完成，共删除 {total_deleted} 条记录')
        
    except Exception as e:
        print(f'批量删除过程中出现错误: {str(e)}')
        raise

# searchdata(15198266,3)
# print(payno,price)
# class mydata:
#     def __init__(self, mycursor, order):
#         self.mycursor = mycursor
#         self.order = order
#
#     def searchdata(self):
#         sql = "select pay_no,need_pay_amount from tv2_pay_bill where order_no=" + self.order + " and pay_status=5"
#         print(sql)
#         self.mycursor.execute(sql)
#         myresult = mycursor.fetchall()
#         return myresult


# data = mydata(mycursor, order)
# data.searchdata()
