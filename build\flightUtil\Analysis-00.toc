(['D:\\work\\LEARNPY\\CODE\\pythonProject\\flightUtil.py'],
 ['D:\\work\\LEARNPY\\CODE\\pythonProject',
  'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages'],
 [],
 ['c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\_impl\\__pyinstaller',
  'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
  'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\_pyinstaller_hooks_contrib'],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.8.6rc1 (tags/v3.8.6rc1:08bd63d, Sep  7 2020, 23:10:23) [MSC v.1927 64 bit '
 '(AMD64)]',
 [('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('flightUtil',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\flightUtil.py',
   'PYSOURCE')],
 [('inspect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\inspect.py',
   'PYMODULE'),
  ('importlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.abc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('contextlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\contextlib.py',
   'PYMODULE'),
  ('configparser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\configparser.py',
   'PYMODULE'),
  ('zipfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\zipfile.py',
   'PYMODULE'),
  ('py_compile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\py_compile.py',
   'PYMODULE'),
  ('lzma',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\lzma.py',
   'PYMODULE'),
  ('_compression',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_compression.py',
   'PYMODULE'),
  ('bz2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\bz2.py',
   'PYMODULE'),
  ('threading',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('struct',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\struct.py',
   'PYMODULE'),
  ('shutil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\gzip.py',
   'PYMODULE'),
  ('copy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\copy.py',
   'PYMODULE'),
  ('fnmatch',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\fnmatch.py',
   'PYMODULE'),
  ('importlib.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('pathlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pathlib.py',
   'PYMODULE'),
  ('urllib.parse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\calendar.py',
   'PYMODULE'),
  ('datetime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\datetime.py',
   'PYMODULE'),
  ('_strptime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_strptime.py',
   'PYMODULE'),
  ('socket',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\selectors.py',
   'PYMODULE'),
  ('random',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\random.py',
   'PYMODULE'),
  ('hashlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pprint.py',
   'PYMODULE'),
  ('_compat_pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\string.py',
   'PYMODULE'),
  ('bisect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\bisect.py',
   'PYMODULE'),
  ('email.feedparser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\getopt.py',
   'PYMODULE'),
  ('gettext',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\gettext.py',
   'PYMODULE'),
  ('quopri',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\quopri.py',
   'PYMODULE'),
  ('uu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\uu.py',
   'PYMODULE'),
  ('optparse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\optparse.py',
   'PYMODULE'),
  ('textwrap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\textwrap.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('csv',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\csv.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('argparse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ast.py',
   'PYMODULE'),
  ('token',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tokenize.py',
   'PYMODULE'),
  ('importlib.machinery',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('dis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\opcode.py',
   'PYMODULE'),
  ('subprocess',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\subprocess.py',
   'PYMODULE'),
  ('signal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\signal.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\netrc.py',
   'PYMODULE'),
  ('shlex',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\shlex.py',
   'PYMODULE'),
  ('mimetypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('ssl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ssl.py',
   'PYMODULE'),
  ('urllib.response',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\http\\client.py',
   'PYMODULE'),
  ('decimal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\contextvars.py',
   'PYMODULE'),
  ('numbers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\numbers.py',
   'PYMODULE'),
  ('hmac',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\hmac.py',
   'PYMODULE'),
  ('tempfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tempfile.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\queue.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\runpy.py',
   'PYMODULE'),
  ('pkgutil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\zipimport.py',
   'PYMODULE'),
  ('multiprocessing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('_py_abc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_py_abc.py',
   'PYMODULE'),
  ('typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\typing.py',
   'PYMODULE'),
  ('stringprep',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\stringprep.py',
   'PYMODULE'),
  ('tracemalloc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.selectors',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\util\\selectors.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.packages',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.ssl_match_hostname',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\packages\\ssl_match_hostname\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.ssl_match_hostname._implementation',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\packages\\ssl_match_hostname\\_implementation.py',
   'PYMODULE'),
  ('ipaddress',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ipaddress.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3.packages.ordered_dict',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\packages\\ordered_dict.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.request',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('uuid',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\uuid.py',
   'PYMODULE'),
  ('ctypes.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('platform',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\platform.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('__future__',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\__future__.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('idna',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs7',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs7.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('typing_extensions',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.log',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('concurrent.futures',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.events',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x509',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x509.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.scrypt',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\scrypt.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x448',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x25519',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.poly1305',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\poly1305.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hmac',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hashes',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed448',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed25519',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dsa',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dh',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.encode_asn1',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\encode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('socks',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('dummy_threading',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('_dummy_thread',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('importlib.resources',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('cgi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\cgi.py',
   'PYMODULE'),
  ('html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\html\\entities.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('chardet',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.version',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langcyrillicmodel',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\langcyrillicmodel.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.escprober',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.enums',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.compat',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\chardet\\compat.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('utils.idGenerator',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\utils\\idGenerator.py',
   'PYMODULE'),
  ('utils',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\utils\\__init__.py',
   'PYMODULE'),
  ('uiauto.IssueTicket',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\uiauto\\IssueTicket.py',
   'PYMODULE'),
  ('uiauto',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\uiauto\\__init__.py',
   'PYMODULE'),
  ('playwright.sync_api',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\sync_api\\__init__.py',
   'PYMODULE'),
  ('playwright.sync_api._context_manager',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\sync_api\\_context_manager.py',
   'PYMODULE'),
  ('playwright._impl._transport',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_transport.py',
   'PYMODULE'),
  ('playwright._impl',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\__init__.py',
   'PYMODULE'),
  ('playwright._impl._helper',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_helper.py',
   'PYMODULE'),
  ('playwright._impl._network',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_network.py',
   'PYMODULE'),
  ('playwright._impl._page',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_page.py',
   'PYMODULE'),
  ('playwright._impl._locator',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py',
   'PYMODULE'),
  ('playwright._impl._video',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_video.py',
   'PYMODULE'),
  ('playwright._impl._js_handle',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_js_handle.py',
   'PYMODULE'),
  ('playwright._impl._map',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_map.py',
   'PYMODULE'),
  ('playwright._impl._input',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_input.py',
   'PYMODULE'),
  ('playwright._impl._har_router',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_har_router.py',
   'PYMODULE'),
  ('playwright._impl._local_utils',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_local_utils.py',
   'PYMODULE'),
  ('playwright._impl._file_chooser',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_file_chooser.py',
   'PYMODULE'),
  ('playwright._impl._element_handle',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_element_handle.py',
   'PYMODULE'),
  ('playwright._impl._set_input_files_helpers',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_set_input_files_helpers.py',
   'PYMODULE'),
  ('playwright._impl._writable_stream',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_writable_stream.py',
   'PYMODULE'),
  ('playwright._impl._download',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_download.py',
   'PYMODULE'),
  ('playwright._impl._console_message',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_console_message.py',
   'PYMODULE'),
  ('playwright._impl._clock',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_clock.py',
   'PYMODULE'),
  ('playwright._impl._artifact',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_artifact.py',
   'PYMODULE'),
  ('playwright._impl._stream',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_stream.py',
   'PYMODULE'),
  ('playwright._impl._accessibility',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_accessibility.py',
   'PYMODULE'),
  ('playwright._impl._frame',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py',
   'PYMODULE'),
  ('pyee',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pyee\\__init__.py',
   'PYMODULE'),
  ('pyee.base',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pyee\\base.py',
   'PYMODULE'),
  ('playwright._impl._fetch',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_fetch.py',
   'PYMODULE'),
  ('playwright._impl._tracing',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_tracing.py',
   'PYMODULE'),
  ('playwright._impl._browser_context',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_browser_context.py',
   'PYMODULE'),
  ('playwright._impl._browser',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_browser.py',
   'PYMODULE'),
  ('playwright._impl._browser_type',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_browser_type.py',
   'PYMODULE'),
  ('playwright._impl._json_pipe',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_json_pipe.py',
   'PYMODULE'),
  ('pyee.asyncio',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pyee\\asyncio.py',
   'PYMODULE'),
  ('playwright._impl._web_error',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_web_error.py',
   'PYMODULE'),
  ('playwright._impl._dialog',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_dialog.py',
   'PYMODULE'),
  ('playwright._impl._cdp_session',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_cdp_session.py',
   'PYMODULE'),
  ('playwright._impl._waiter',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_waiter.py',
   'PYMODULE'),
  ('playwright._impl._event_context_manager',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_event_context_manager.py',
   'PYMODULE'),
  ('playwright._impl._str_utils',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_str_utils.py',
   'PYMODULE'),
  ('playwright._impl._glob',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_glob.py',
   'PYMODULE'),
  ('playwright._impl._driver',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_driver.py',
   'PYMODULE'),
  ('playwright._repo_version',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_repo_version.py',
   'PYMODULE'),
  ('playwright._impl._playwright',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_playwright.py',
   'PYMODULE'),
  ('playwright._impl._selectors',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_selectors.py',
   'PYMODULE'),
  ('playwright._impl._object_factory',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_object_factory.py',
   'PYMODULE'),
  ('playwright._impl._greenlets',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_greenlets.py',
   'PYMODULE'),
  ('playwright._impl._connection',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py',
   'PYMODULE'),
  ('greenlet',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('playwright._impl._assertions',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_assertions.py',
   'PYMODULE'),
  ('playwright.sync_api._generated',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py',
   'PYMODULE'),
  ('playwright._impl._sync_base',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py',
   'PYMODULE'),
  ('playwright._impl._impl_to_api_mapping',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_impl_to_api_mapping.py',
   'PYMODULE'),
  ('playwright',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\__init__.py',
   'PYMODULE'),
  ('playwright._impl._errors',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_errors.py',
   'PYMODULE'),
  ('playwright._impl._api_structures',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\playwright\\_impl\\_api_structures.py',
   'PYMODULE'),
  ('selenium.webdriver.common.by',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\by.py',
   'PYMODULE'),
  ('selenium.webdriver.common',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.service',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.common.utils',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.common.keys',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\keys.py',
   'PYMODULE'),
  ('selenium.types',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\types.py',
   'PYMODULE'),
  ('selenium.common.exceptions',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\common\\exceptions.py',
   'PYMODULE'),
  ('selenium.common',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\common\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.chrome',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\webdriver_manager\\chrome.py',
   'PYMODULE'),
  ('webdriver_manager',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\webdriver_manager\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.drivers.chrome',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\webdriver_manager\\drivers\\chrome.py',
   'PYMODULE'),
  ('webdriver_manager.drivers',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\webdriver_manager\\drivers\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.core.logger',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\webdriver_manager\\core\\logger.py',
   'PYMODULE'),
  ('webdriver_manager.core',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\webdriver_manager\\core\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.core.config',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\webdriver_manager\\core\\config.py',
   'PYMODULE'),
  ('dotenv',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.variables',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('dotenv.parser',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\webdriver_manager\\core\\driver.py',
   'PYMODULE'),
  ('webdriver_manager.core.utils',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\webdriver_manager\\core\\utils.py',
   'PYMODULE'),
  ('webdriver_manager.core.archive',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\webdriver_manager\\core\\archive.py',
   'PYMODULE'),
  ('webdriver_manager.core.manager',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\webdriver_manager\\core\\manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver_cache',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\webdriver_manager\\core\\driver_cache.py',
   'PYMODULE'),
  ('webdriver_manager.core.constants',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\webdriver_manager\\core\\constants.py',
   'PYMODULE'),
  ('webdriver_manager.core.download_manager',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\webdriver_manager\\core\\download_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.http',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\webdriver_manager\\core\\http.py',
   'PYMODULE'),
  ('selenium.webdriver',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.proxy',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\proxy.py',
   'PYMODULE'),
  ('selenium.webdriver.common.action_chains',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\action_chains.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webelement',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\webelement.py',
   'PYMODULE'),
  ('selenium.webdriver.remote',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.utils',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.shadowroot',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\shadowroot.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.command',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\command.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.action_builder',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\action_builder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_actions',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_input',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.input_device',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\input_device.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_actions',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.mouse_button',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\mouse_button.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_input',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_actions',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.interaction',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\interaction.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_input',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.desired_capabilities',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\desired_capabilities.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webdriver',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.remote_connection',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\firefox\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\firefox\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.remote_connection',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\safari\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.safari',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\safari\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.remote_connection',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\chromium\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\chromium\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.service',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\chromium\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.common.virtual_authenticator',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\virtual_authenticator.py',
   'PYMODULE'),
  ('selenium.webdriver.support.relative_locator',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\support\\relative_locator.py',
   'PYMODULE'),
  ('selenium.webdriver.support',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\support\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.html5.application_cache',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\html5\\application_cache.py',
   'PYMODULE'),
  ('selenium.webdriver.common.html5',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\html5\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.timeouts',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\timeouts.py',
   'PYMODULE'),
  ('selenium.webdriver.common.print_page_options',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\print_page_options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.options',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.switch_to',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\switch_to.py',
   'PYMODULE'),
  ('selenium.webdriver.common.alert',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\alert.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.script_key',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\script_key.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.remote_connection',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.mobile',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\mobile.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.file_detector',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\file_detector.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.errorhandler',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\errorhandler.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.bidi_connection',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\bidi_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.options',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.webdriver',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.service',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.options',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.webdriver',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.service',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\webkitgtk\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.webdriver',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\safari\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.service',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\safari\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.options',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\safari\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.opera.webdriver',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\opera\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.opera',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\opera\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.opera.options',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\opera\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.options',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\edge\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.edge',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\edge\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.options',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\chromium\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.webdriver',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\edge\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.service',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\edge\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.webdriver',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\chromium\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.options',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\ie\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.ie',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\ie\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.webdriver',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\ie\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.service',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\ie\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.options',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\chrome\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\chrome\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.webdriver',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\chrome\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.service',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\chrome\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.options',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\firefox\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_binary',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_binary.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_profile',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_profile.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.webdriver',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.service',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\firefox\\service.py',
   'PYMODULE'),
  ('selenium',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\__init__.py',
   'PYMODULE'),
  ('database.dataconnect',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\database\\dataconnect.py',
   'PYMODULE'),
  ('database',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\database\\__init__.py',
   'PYMODULE'),
  ('utils.DbUtil',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\utils\\DbUtil.py',
   'PYMODULE'),
  ('pymysql',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pymysql\\__init__.py',
   'PYMODULE'),
  ('pymysql.connections',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pymysql\\connections.py',
   'PYMODULE'),
  ('pymysql.protocol',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pymysql\\protocol.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pymysql\\optionfile.py',
   'PYMODULE'),
  ('pymysql.cursors',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pymysql\\cursors.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pymysql\\constants\\SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pymysql\\constants\\CR.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pymysql\\constants\\COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pymysql\\constants\\CLIENT.py',
   'PYMODULE'),
  ('pymysql.charset',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pymysql\\charset.py',
   'PYMODULE'),
  ('pymysql.converters',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pymysql\\converters.py',
   'PYMODULE'),
  ('pymysql._auth',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pymysql\\_auth.py',
   'PYMODULE'),
  ('pymysql.times',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pymysql\\times.py',
   'PYMODULE'),
  ('pymysql.err',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pymysql\\err.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pymysql\\constants\\ER.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pymysql\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('pymysql.constants',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\pymysql\\constants\\__init__.py',
   'PYMODULE'),
  ('utils.testUtil',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\utils\\testUtil.py',
   'PYMODULE')],
 [('playwright\\driver\\package\\bin\\PrintDeps.exe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\PrintDeps.exe',
   'BINARY'),
  ('playwright\\driver\\node.exe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\node.exe',
   'BINARY'),
  ('python38.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\python38.dll',
   'BINARY'),
  ('_lzma.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_openssl.pyd',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_openssl.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp38-win_amd64.pyd',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\_cffi_backend.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp38-win_amd64.pyd',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\greenlet\\_greenlet.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libffi-7.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('python3.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\python3.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\Windows\\system32\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\build\\flightUtil\\base_library.zip',
   'DATA'),
  ('cryptography-37.0.2.dist-info\\LICENSE.PSF',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography-37.0.2.dist-info\\LICENSE.PSF',
   'DATA'),
  ('cryptography-37.0.2.dist-info\\top_level.txt',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography-37.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('cryptography-37.0.2.dist-info\\WHEEL',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography-37.0.2.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-37.0.2.dist-info\\LICENSE.APACHE',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography-37.0.2.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-37.0.2.dist-info\\METADATA',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography-37.0.2.dist-info\\METADATA',
   'DATA'),
  ('cryptography-37.0.2.dist-info\\INSTALLER',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography-37.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-37.0.2.dist-info\\LICENSE.BSD',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography-37.0.2.dist-info\\LICENSE.BSD',
   'DATA'),
  ('cryptography-37.0.2.dist-info\\RECORD',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography-37.0.2.dist-info\\RECORD',
   'DATA'),
  ('cryptography-37.0.2.dist-info\\LICENSE',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\cryptography-37.0.2.dist-info\\LICENSE',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\oopDownloadBrowserMain.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\oopDownloadBrowserMain.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\android.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\android.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\socksClientCertificatesInterceptor.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\socksClientCertificatesInterceptor.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\types.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\nativeDeps.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\nativeDeps.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browserType.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\browserType.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\test\\inMemorySnapshotter.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\test\\inMemorySnapshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\browserFetcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\browserFetcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\fetch.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\fetch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fetch.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\fetch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkPage.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.DloKQa-h.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.DloKQa-h.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\jsHandle.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\jsHandle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-BW-aOBcL.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-BW-aOBcL.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\codicon.DCmgc-ay.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\codicon.DCmgc-ay.ttf',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crPage.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crPage.js',
   'DATA'),
  ('playwright\\driver\\package\\types\\types.d.ts',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\types\\types.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\webSocketMockSource.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\webSocketMockSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\deviceDescriptorsSource.json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\deviceDescriptorsSource.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\recorderUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\recorderUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\electron\\loader.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\electron\\loader.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\wsServer.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\wsServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\worker.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\worker.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\browser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\ascii.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\ascii.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\api.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\api.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\crypto.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\crypto.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\electronDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\electronDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\validatorPrimitives.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\validatorPrimitives.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\coverage.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\coverage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\java.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\java.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\testServerConnection-DeE2kSzz.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\testServerConnection-DeE2kSzz.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\dependencies.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\dependencies.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\embedded.Do_J5Hgs.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\embedded.Do_J5Hgs.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\channelOwner.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\channelOwner.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\registry\\index.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\registry\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\download.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\download.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\javascript.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\javascript.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\zones.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\zones.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\stackTrace.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\stackTrace.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\stream.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\stream.js',
   'DATA'),
  ('playwright\\driver\\package\\index.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\common\\timeoutSettings.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\common\\timeoutSettings.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-BcaUAUCW.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\index-BcaUAUCW.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\accessibility.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\accessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiSerializer.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiSerializer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\outofprocess.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\outofprocess.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\consoleMessage.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\consoleMessage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\xtermModule.DSXBckUd.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\xtermModule.DSXBckUd.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\pipeTransport.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\pipeTransport.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\viewer\\traceViewer.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\viewer\\traceViewer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\languages.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\languages.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\network.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotter.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\debugControllerDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\debugControllerDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserTypeDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserTypeDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiPage.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiInput.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\localUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\localUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\protocol.yml',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\protocol.yml',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_mac.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\electron\\electron.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\electron\\electron.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.B21BXreT.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.B21BXreT.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dialog.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dialog.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\workbench.D3JVcA9K.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\workbench.D3JVcA9K.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffConnection.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\spawnAsync.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\spawnAsync.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\artifact.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\artifact.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\codeMirrorModule.ez37Vkbh.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\codeMirrorModule.ez37Vkbh.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\dialogDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\dialogDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\transport.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\transport.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\validator.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\validator.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\launchApp.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\launchApp.js',
   'DATA'),
  ('playwright\\driver\\package\\cli.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\cli.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\connection.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\connection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\timeoutRunner.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\timeoutRunner.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.CrbWWHbf.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.CrbWWHbf.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crBrowser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\browsers.json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\browsers.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browserContext.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\browserContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiKeyboard.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiKeyboard.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\htmlReport\\index.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\htmlReport\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkWorkers.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkWorkers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\playwright.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\playwright.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiConnection.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\frames.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\frames.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\defaultFontFamilies.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\defaultFontFamilies.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\language.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\language.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\playwrightDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\playwrightDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\task.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\task.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiPdf.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiPdf.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffPage.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffPage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderInTraceViewer.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderInTraceViewer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\progress.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\progress.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_mac.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\androidServerImpl.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\androidServerImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\android\\android.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\android\\android.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\urlMatch.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\urlMatch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\utilityScriptSource.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\utilityScriptSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\macEditingCommands.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\macEditingCommands.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\clock.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\clock.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiBrowser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\types.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\recorder.B_SY1GJM.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\recorder.B_SY1GJM.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\compare.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\compare.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\httpServer.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\httpServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\time.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\time.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crPdf.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crPdf.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\debugController.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\debugController.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.CAYqod-m.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.CAYqod-m.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\headers.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\headers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\formData.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\formData.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\browserType.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\browserType.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_linux.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_win.ps1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\bin\\README.md',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\README.md',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_win.ps1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\pageDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\pageDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\driver.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\cli\\driver.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\env.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\env.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\embedded.w7WN2u1R.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\embedded.w7WN2u1R.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fileChooser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\fileChooser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\transport.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\transport.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crCoverage.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crCoverage.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\artifactDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\artifactDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\zipBundle.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\zipBundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crServiceWorker.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crServiceWorker.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\writableStreamDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\writableStreamDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\harRouter.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\harRouter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crAccessibility.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\selectorsDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\selectorsDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\artifact.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\artifact.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crProtocolHelper.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crProtocolHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\traceUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\traceUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\webSocketRouteDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\webSocketRouteDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\manualPromise.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\manualPromise.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkConnection.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\userAgent.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\userAgent.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\locator.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\locator.js',
   'DATA'),
  ('playwright\\driver\\package\\index.mjs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\index.mjs',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\zipFile.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\zipFile.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\videoRecorder.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\videoRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\happy-eyeballs.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\happy-eyeballs.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkExecutionContext.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\elementHandle.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\elementHandle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\playwright-logo.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\playwright-logo.svg',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\inspectorTab-DTusvprx.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\inspectorTab-DTusvprx.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clientInstrumentation.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\clientInstrumentation.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crDevTools.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crDevTools.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\fileChooser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\fileChooser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\programWithTestStub.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\cli\\programWithTestStub.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\inspectorTab.DLjBDrQR.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\inspectorTab.DLjBDrQR.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\page.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\page.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\errors.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\errors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundleImpl\\xdg-open',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utilsBundleImpl\\xdg-open',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\multimap.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\multimap.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\javascript.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\javascript.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\waiter.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\waiter.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_win.ps1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\network.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\workbench-DIEjrm3Z.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\workbench-DIEjrm3Z.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\selectors.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\selectors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\console.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\console.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crDragDrop.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crDragDrop.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\cssParser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\cssParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\appIcon.png',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\appIcon.png',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\browser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\zipBundleImpl.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\zipBundleImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\selectors.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\selectors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\electron.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\electron.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\socksInterceptor.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\socksInterceptor.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\remote\\playwrightServer.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\remote\\playwrightServer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderFrontend.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderFrontend.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\hostPlatform.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\hostPlatform.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkInterceptableRequest.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkInterceptableRequest.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\throttledFile.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\throttledFile.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\tracing.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\tracing.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\expectUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\expectUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiProtocol.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiProtocol.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clock.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\clock.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\network.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\network.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffExecutionContext.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\tracing.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\tracing.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\usKeyboardLayout.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\usKeyboardLayout.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\chromium.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\chromium.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\browserContext.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\browserContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\cookieStore.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\cookieStore.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\jsHandleDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\jsHandleDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\injectedScriptSource.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\injectedScriptSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\rtti.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\rtti.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderCollection.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderCollection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\dispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\dispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\processLauncher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\processLauncher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codicon-DCmgc-ay.ttf',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codicon-DCmgc-ay.ttf',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\embedded.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\embedded.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\tracingDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\tracingDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffAccessibility.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crNetworkManager.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crInput.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crInput.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\jsonl.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\jsonl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\input.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\input.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\debugLogger.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\debugLogger.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\inprocess.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\inprocess.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\cdpSession.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\cdpSession.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorGenerators.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorGenerators.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\common\\types.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\common\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\types.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\types.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-CR6kB851.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-CR6kB851.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\imageChannel.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\imageChannel.js',
   'DATA'),
  ('playwright\\driver\\package\\types\\structs.d.ts',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\types\\structs.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\pollingRecorderSource.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\pollingRecorderSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\androidDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\androidDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\streamDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\streamDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiChromium.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiChromium.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\cdpSessionDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\cdpSessionDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkAccessibility.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkAccessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkProvisionalPage.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkProvisionalPage.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_win.ps1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\networkDispatchers.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\networkDispatchers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\selectorParser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\selectorParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\instrumentation.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\instrumentation.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiDeserializer.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\bidiDeserializer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\cli\\program.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\cli\\program.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\common\\socksProxy.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\common\\socksProxy.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundleImpl\\index.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utilsBundleImpl\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\python.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\python.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\inProcessFactory.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\inProcessFactory.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\index.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\ThirdPartyNotices.txt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\ThirdPartyNotices.txt',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\errors.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\errors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\consoleApiSource.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\consoleApiSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\remote\\playwrightConnection.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\remote\\playwrightConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\playwright-logo.svg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\playwright-logo.svg',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotterInjected.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\trace\\recorder\\snapshotterInjected.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crExecutionContext.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\sw.bundle.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\sw.bundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\index.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\index.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\codegen\\csharp.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\codegen\\csharp.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_stable_mac.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_stable_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\localUtilsDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\localUtilsDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\fileUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\fileUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\screenshotter.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\screenshotter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiFirefox.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiFirefox.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\stats.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\stats.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\protocolError.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\protocolError.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\har\\harTracer.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\har\\harTracer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\jsonPipeDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\jsonPipeDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\har\\harRecorder.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\har\\harRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\traceUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\traceUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\elementHandlerDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\elementHandlerDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_mac.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\input.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\input.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\chromiumSwitches.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\chromiumSwitches.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\frameSelectors.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\frameSelectors.js',
   'DATA'),
  ('playwright\\driver\\LICENSE',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\LICENSE',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-ez37Vkbh.css',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\recorder\\assets\\codeMirrorModule-ez37Vkbh.css',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiNetworkManager.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\generated\\clockSource.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\generated\\clockSource.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\codeMirrorModule-CZTtn9l8.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\codeMirrorModule-CZTtn9l8.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_beta_linux.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_beta_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\page.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\page.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\playwright.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\playwright.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiExecutionContext.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiExecutionContext.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\profiler.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\profiler.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\isomorphic\\utilityScriptSerializers.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\isomorphic\\utilityScriptSerializers.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_linux.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\third_party\\firefoxPrefs.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\third_party\\firefoxPrefs.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\recorder.Bfh_9UGt.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\recorder.Bfh_9UGt.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\recorder.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\recorder.html',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_linux.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\index.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\helper.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\helper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\xtermModule-BeNbaIVa.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\assets\\xtermModule-BeNbaIVa.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\android\\backendAdb.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\android\\backendAdb.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\jsonPipe.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\jsonPipe.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffBrowser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffNetworkManager.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffNetworkManager.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkBrowser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkBrowser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\cssTokenizer.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\cssTokenizer.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\chromium\\crConnection.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\chromium\\crConnection.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\firefox.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\firefox.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dom.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dom.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\image_tools\\colorUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\image_tools\\colorUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\api.json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\api.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\semaphore.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\semaphore.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\eventEmitter.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\eventEmitter.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\index.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\index.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\webkit.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\webkit.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\events.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\events.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\uiMode.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\deviceDescriptors.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\deviceDescriptors.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\webkit\\wkInput.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\webkit\\wkInput.js',
   'DATA'),
  ('playwright\\driver\\package\\README.md',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\README.md',
   'DATA'),
  ('playwright\\driver\\package\\package.json',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\package.json',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderRunner.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderRunner.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\video.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\video.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\clientHelper.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\clientHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\linuxUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\linuxUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\eventsHelper.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\eventsHelper.js',
   'DATA'),
  ('playwright\\driver\\package\\index.d.ts',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\index.d.ts',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\recorderApp.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\recorderApp.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_msedge_dev_mac.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_msedge_dev_mac.sh',
   'DATA'),
  ('playwright\\driver\\package\\bin\\install_media_pack.ps1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\install_media_pack.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\browserServerImpl.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\browserServerImpl.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\comparators.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\comparators.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\third_party\\pixelmatch.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\third_party\\pixelmatch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\recorder\\contextRecorder.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\recorder\\contextRecorder.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\vite\\traceViewer\\snapshot.html',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\vite\\traceViewer\\snapshot.html',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\mimeType.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\mimeType.js',
   'DATA'),
  ('playwright\\driver\\README.md',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\README.md',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\frame.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\frame.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\browserContextDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\browserContextDispatcher.js',
   'DATA'),
  ('playwright\\py.typed',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\py.typed',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utilsBundle.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utilsBundle.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\debug.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\debug.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\webError.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\webError.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\accessibility.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\accessibility.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\bidi\\bidiOverCdp.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\bidi\\bidiOverCdp.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\debugger.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\debugger.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\stringUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\stringUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_stable_win.ps1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_stable_win.ps1',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\download.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\download.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\dialog.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\dialog.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\firefox\\ffInput.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\firefox\\ffInput.js',
   'DATA'),
  ('playwright\\driver\\package\\bin\\reinstall_chrome_beta_linux.sh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\bin\\reinstall_chrome_beta_linux.sh',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\fileUploadUtils.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\fileUploadUtils.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorParser.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\isomorphic\\locatorParser.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\server\\dispatchers\\frameDispatcher.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\server\\dispatchers\\frameDispatcher.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\protocol\\serializers.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\protocol\\serializers.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\utils\\debug.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\utils\\debug.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\third_party\\diff_match_patch.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\third_party\\diff_match_patch.js',
   'DATA'),
  ('playwright\\driver\\package\\lib\\client\\writableStream.js',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\lib\\client\\writableStream.js',
   'DATA'),
  ('playwright\\driver\\package\\types\\protocol.d.ts',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python38\\lib\\site-packages\\playwright\\driver\\package\\types\\protocol.d.ts',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v100\\py.typed',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v100\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v85\\py.typed',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v85\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v101\\py.typed',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v101\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'DATA'),
  ('selenium\\webdriver\\remote\\findElements.js',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\findElements.js',
   'DATA'),
  ('selenium\\py.typed',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\remote\\getAttribute.js',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\getAttribute.js',
   'DATA'),
  ('selenium\\webdriver\\remote\\isDisplayed.js',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\isDisplayed.js',
   'DATA'),
  ('selenium\\webdriver\\common\\mutation-listener.js',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\mutation-listener.js',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v102\\py.typed',
   'D:\\work\\LEARNPY\\CODE\\pythonProject\\venv\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v102\\py.typed',
   'DATA')])
