#!/usr/bin/env python
# -*- coding: utf-8 -*-

import random
import datetime
from typing import List, Dict, Tuple, Optional, Union

class IDGenerator:
    """
    虚拟身份证和姓名生成器
    可以指定年龄或随机生成，每次生成10条信息
    """
    
    # 常用汉字姓氏
    SURNAMES = [
        "赵", "钱", "孙", "李", "周", "吴", "郑", "王", "冯", "陈", "褚", "卫", "蒋", "沈", "韩", "杨", "朱", "秦", "尤", "许",
        "何", "吕", "施", "张", "孔", "曹", "严", "华", "金", "魏", "陶", "姜", "戚", "谢", "邹", "喻", "柏", "水", "窦", "章",
        "云", "苏", "潘", "葛", "奚", "范", "彭", "郎", "鲁", "韦", "昌", "马", "苗", "凤", "花", "方", "俞", "任", "袁", "柳",
        "酆", "鲍", "史", "唐", "费", "廉", "岑", "薛", "雷", "贺", "倪", "汤", "滕", "殷", "罗", "毕", "郝", "邬", "安", "常",
        "乐", "于", "时", "傅", "皮", "卞", "齐", "康", "伍", "余", "元", "卜", "顾", "孟", "平", "黄", "和", "穆", "萧", "尹"
    ]
    
    # 常用汉字名字
    NAMES = [
        "伟", "芳", "娜", "秀英", "敏", "静", "丽", "强", "磊", "军", "洋", "勇", "艳", "杰", "娟", "涛", "明", "超", "秀兰",
        "霞", "平", "刚", "桂英", "博", "志强", "建华", "建国", "建军", "小红", "小明", "小刚", "小芳", "小霞", "小军", "小强",
        "小伟", "小芬", "小玲", "小娟", "小华", "小燕", "小玉", "小龙", "小风", "小雨", "小雪", "小云", "小梅", "小花", "小草",
        "小鹏", "小鸟", "小虎", "小狗", "小猫", "小鱼", "小牛", "小羊", "小马", "小猴", "小熊", "小兔", "小狐", "小狼", "小鹿",
        "小象", "小鹰", "小鸡", "小鸭", "小鹅", "小猪", "小蛇", "小龟", "小鳄", "小鲨", "小鲸", "小海", "小江", "小河", "小湖"
    ]
    
    # 地区编码（部分常用地区）
    AREA_CODES = {
        "北京市": ["110100", "110101", "110102", "110105", "110106", "110107", "110108", "110109", "110111", "110112", "110113", "110114", "110115", "110116", "110117", "110118", "110119"],
        "上海市": ["310100", "310101", "310104", "310105", "310106", "310107", "310109", "310110", "310112", "310113", "310114", "310115", "310116", "310117", "310118", "310120", "310151"],
        "广州市": ["440100", "440103", "440104", "440105", "440106", "440111", "440112", "440113", "440114", "440115", "440117", "440118"],
        "深圳市": ["440300", "440303", "440304", "440305", "440306", "440307", "440308", "440309", "440310", "440311"],
        "杭州市": ["330100", "330102", "330103", "330104", "330105", "330106", "330108", "330109", "330110", "330111", "330112", "330122", "330127", "330182", "330183"],
        "南京市": ["320100", "320102", "320104", "320105", "320106", "320111", "320113", "320114", "320115", "320116", "320117", "320118"],
        "成都市": ["510100", "510104", "510105", "510106", "510107", "510108", "510112", "510113", "510114", "510115", "510116", "510117", "510121", "510129", "510131", "510132", "510181"],
        "重庆市": ["500100", "500101", "500102", "500103", "500104", "500105", "500106", "500107", "500108", "500109", "500110", "500111", "500112", "500113", "500114", "500115", "500116", "500117", "500118", "500119", "500120", "500151", "500152", "500153", "500154"]
    }
    
    @staticmethod
    def generate_random_date(start_year: int, end_year: int) -> str:
        """生成指定年份范围内的随机日期"""
        year = random.randint(start_year, end_year)
        month = random.randint(1, 12)
        
        # 处理不同月份的天数
        if month in [4, 6, 9, 11]:
            day = random.randint(1, 30)
        elif month == 2:
            # 处理闰年
            if (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0):
                day = random.randint(1, 29)
            else:
                day = random.randint(1, 28)
        else:
            day = random.randint(1, 31)
            
        return f"{year:04d}{month:02d}{day:02d}"
    
    @staticmethod
    def calculate_check_digit(id_number: str) -> str:
        """计算身份证校验码"""
        # 加权因子
        factors = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        # 校验码对应值
        check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
        
        # 计算加权和
        sum_weighted = 0
        for i in range(17):
            sum_weighted += int(id_number[i]) * factors[i]
            
        # 计算校验码
        check_digit = check_codes[sum_weighted % 11]
        return check_digit
    
    @classmethod
    def generate_id_card(cls, age: Optional[int] = None) -> Tuple[str, str, int]:
        """
        生成随机身份证号
        
        Args:
            age: 指定年龄，如果为None则随机生成(18-70岁)
            
        Returns:
            Tuple[str, str, int]: (身份证号, 性别, 年龄)
        """
        # 随机选择地区
        city = random.choice(list(cls.AREA_CODES.keys()))
        area_code = random.choice(cls.AREA_CODES[city])
        
        # 生成出生日期
        current_year = datetime.datetime.now().year
        if age is None:
            # 随机年龄 (1-200岁)
            age = random.randint(1, 200)
        
        birth_year = current_year - age
        birth_date = cls.generate_random_date(birth_year, birth_year)
        
        # 生成顺序码前2位 (2位)
        sequence_code_prefix = f"{random.randint(1, 99):02d}"

        # 性别码 (奇数为男，偶数为女) - 这是顺序码的第3位
        gender_code = random.choice([1, 3, 5, 7, 9]) if random.choice([True, False]) else random.choice([0, 2, 4, 6, 8])
        gender = "男" if gender_code % 2 == 1 else "女"

        # 组合前17位 (6位地区码 + 8位出生日期 + 3位顺序码)
        id_number_17 = f"{area_code}{birth_date}{sequence_code_prefix}{gender_code}"
        
        # 计算校验码
        check_digit = cls.calculate_check_digit(id_number_17)
        
        # 完整身份证号
        id_number = id_number_17 + check_digit
        
        return id_number, gender, age
    
    @classmethod
    def generate_name(cls, gender: str) -> str:
        """根据性别生成随机姓名"""
        surname = random.choice(cls.SURNAMES)
        
        # 根据性别选择更可能的名字
        if gender == "男":
            name_pool = [n for n in cls.NAMES if len(n) == 1 or n in ["建国", "建军", "志强", "建华", "小刚", "小明", "小龙", "小虎"]]
        else:
            name_pool = [n for n in cls.NAMES if n in ["秀英", "桂英", "小红", "小芳", "小霞", "小芬", "小玲", "小娟", "小华", "小燕", "小玉"]]
        
        # 如果筛选后的池子为空，则使用全部名字
        if not name_pool:
            name_pool = cls.NAMES
            
        name = random.choice(name_pool)
        return surname + name
    
    @classmethod
    def generate_identity(cls, age: Optional[int] = None) -> Dict[str, str]:
        """生成一个完整的虚拟身份信息"""
        id_number, gender, actual_age = cls.generate_id_card(age)
        name = cls.generate_name(gender)
        
        return {
            "姓名": name,
            "性别": gender,
            "年龄": str(actual_age),
            "身份证号": id_number
        }
    
    @classmethod
    def generate_identities(cls, count: int = 10, age: Optional[int] = None) -> List[Dict[str, str]]:
        """生成多个虚拟身份信息"""
        return [cls.generate_identity(age) for _ in range(count)]
