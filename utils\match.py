# import null as null
import requests




# reqone = requests.get("http://fticketdev.feeyo.com/sapi/miao/test", timeout=30)  # 请求连接
# reqtwo= requests.get("http://fticketdev.feeyo.com/sapi/miao/phpSearch?dep=CAN&arr=INC&date=2022-07-20", timeout=30)
reqone = requests.get("http://fticketdev.feeyo.com/tv2/Coupontest/list?uid=55545647&pid=64", timeout=30)  # 请求连接
reqtwo= requests.get("http://openapi.variflight.com/sapi/coupon/list?timestamp=1678870816&token=123&uid=55545647&pid=64&unique_id=&debug=1", timeout=30)
reqqq1 = reqone.json()  # 获取数据
reqqq2 = reqtwo.json()

# req_json=reqqq1[-1]['tripDtoList']
req_json=reqqq1['data']
old_json=reqqq2['data']

new=[]
old=[]

def match_xingcheng(newlist,oldlist,flightno,erroflight):
    newold={
        'depPortCode':'depPortCode',
        'distance':'distance',
        'actFlightNo':'actFlightNo',
        'alCode':'alCode',
        'arrPortCode':'arrPortCode',
        'depCityCode':'depCityCode',
        'arrCityCode':'arrCityCode',
        'depT':'depT',
        'arrT': 'arrT',
        'depPlanTime': 'depTime',
        'depDate': 'depDate',
        'arrPlanTime': 'arrTime',
        'share': 'share',
        'stop': 'stop',
        'craftType': 'aircraftType',
        'meal': 'meal',
        'oil': 'oilFee',
        'tax': 'tax',
    }

    for index,key in enumerate(newold):
        if key=='stop':
            print('新{}与老{}是否等值'.format(key, newold[key]), newlist[key], oldlist[newold[key]],
                  str(newlist[key]) ==str(oldlist[newold[key]]))
            if str(newlist[key]) !=str(oldlist[newold[key]]):
                if flightno not in erroflight:
                    erroflight.append(flightno)
        else:
            print('新{}与老{}是否等值'.format(key,newold[key]),newlist[key],oldlist[newold[key]],newlist[key]==oldlist[newold[key]])
            if newlist[key] != oldlist[newold[key]]:
                if flightno not in erroflight:
                    erroflight.append(flightno)

def match_policy(newpolicy,oldpolicy,oldwid,errowid):
    newold={
        'pid':'pid'
    }
    for index,key in enumerate(newold):
        print('新{}与老{}是否等值'.format(key, newold[key]), newpolicy[key], oldpolicy[newold[key]],
              newpolicy[key] == oldpolicy[newold[key]])
        if newpolicy[key] != oldpolicy[newold[key]]:
              if oldwid not in errowid:
                  errowid.append(oldwid)

#由航班匹配老接口具体方案，获取new和old的数据
def start(req_json,old_json,new):
    # cabinDto=[]
    # flightListDto=[]
    erroflight=[]
    errowid=[]
    flightlist = []
    widlist=[]
    for i,req_json1 in enumerate(req_json):
        # print(req_json1)
        if req_json1['planType'] ==3:
            new.append(req_json1)
    for i,aold in enumerate(old_json):
        haveflight=0

        # print(flightno)
        for i1,anew in enumerate(new):
            flightno = anew['planKey'][-6:]
            if aold['flightNo']==flightno:
                haveflight=1
                print(aold['flightNo'], flightno)
                flightListDto=anew['segmentDtoList'][0]['flightListDto']
                match_xingcheng(flightListDto, aold,flightno, erroflight)
                for i2,oldpolicy in enumerate(aold['cabinList']):
                    havewid=0

                    for i3,newpolicy in enumerate(anew['productGroupDtoList']):
                        if newpolicy['productId']==oldpolicy['wid']:
                            havewid=1
                            print(oldpolicy['wid'],newpolicy['productId'])
                            match_policy(newpolicy['productDtoList'][0]['cabinDto'],oldpolicy,oldpolicy['wid'],errowid)
                            break
                    if havewid==0:
                        widlist.append('新接口无wid对应{}'.format(oldpolicy['wid']))
                break
        if haveflight==0:
            flightlist.append('新接口无航班对应{}'.format(aold['flightNo']))
    print('存在错误数据的航班号', erroflight)
    print('存在错误数据的wid', errowid)
    print('新接口有无航班对应',flightlist)
    for index,aa in enumerate(widlist):
        print(aa)
try :
    start(req_json,old_json,new)
except Exception as e :
    print(e)


# #将new数据循环遍历结构
# def start1(req_json):
#     if req_json==None or req_json==False or req_json==True or req_json==[] or req_json== {}:
#         pass
#     else:
#         for i,req_json1 in enumerate(req_json):
#             if isinstance(req_json1,dict):
#                 for i2,req_json2 in enumerate(req_json1):
#                     print(req_json1[req_json2])
#                     break
#                     circulate_data(req_json2,req_json1[req_json2])
#             elif isinstance(req_json[2],list):
#                 circulate_data(1,req_json1)
#             else:
#                 match_data()
# def circulate_data(duiyingziduan,req_json):
#     if req_json == None or req_json == False or req_json == True or req_json == [] or req_json == {}:
#         pass
#     else:
#         for i,req_json1 in enumerate(req_json):
#             if isinstance(req_json1,dict):
#                 for i2,req_json2 in enumerate(req_json1):
#                     print(req_json1[req_json2])
#                     break
#                     circulate_data(req_json2,req_json1[req_json2])
#             elif isinstance(req_json[2],list):
#                 circulate_data(req_json1)
#             else:
#                 match_data(duiyingziduan,req_json)
