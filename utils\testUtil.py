import requests

def request_data(url):
    try:
        req_json={}
        req = requests.get(url, timeout=30)  # 请求连接
        # req_json=req.json()
        is_str=req.headers['Content-Type'].find('application/json')
        if is_str==-1:
            req_json=req.text
        else:
            req_json=req.json()


        return req_json

    except Exception as e:
        req_json['erro']=e
        return req_json


