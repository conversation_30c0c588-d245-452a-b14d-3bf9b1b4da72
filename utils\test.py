import base64
import gzip
import io
import requests

def uncompress(compressed):
    """
    解压缩Base64编码的GZIP压缩数据
    
    Args:
        compressed: Base64编码的压缩字符串
        
    Returns:
        解压缩后的字符串，失败则返回None
    """
    try:
        # 解码Base64
        decoded_data = base64.b64decode(compressed)
        
        # 使用gzip解压缩
        with gzip.GzipFile(fileobj=io.BytesIO(decoded_data), mode='rb') as gunzip:
            decompressed_data = gunzip.read()
        
        # 转换为字符串
        result = decompressed_data.decode('utf-8')
        return result
    except Exception as e:
        print(f"解压失败: {e}")
        return None

def fetch_and_uncompress():
    # url = 'https://fticketdev.variflight.com/admin/test/naChannelJava?channel=233&segment=[{"depCityCode":"BJS","arrCityCode":"HKG","depDate":"2025-07-30"}]&cabinClass=["Economy","Business"]&passenger={"ADT":1}&searchType=9'
    url = 'https://fticketdev.variflight.com/admin/test/naChannelJava?channel=234&segment=[{"depCityCode":"BJS","arrCityCode":"HKG","depDate":"2025-08-05"}]&cabinClass=["Economy","Business"]&passenger={"ADT":1,"CHD":1}&searchType=9'
    response = requests.get(url)
    response.raise_for_status()
    # print(response.text)
    print(uncompress(response.text))

if __name__ == "__main__":
    fetch_and_uncompress()
