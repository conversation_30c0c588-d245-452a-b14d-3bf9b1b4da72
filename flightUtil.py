from utils.testUtil import request_data
from database.dataconnect import searchdata
from uiauto.IssueTicket import autoIssueTicket,autoIssueTicketPlaywright
from utils.idGenerator import IDGenerator
import requests
import time
# from mysql.connector.locales.eng import client_error  #解决打包后执行sql语句时报错：ImportError: No localization support for language 'eng' in python

data = {
    "qudao_url": "",
    # "http://fticketdev.feeyo.com/sapi/fare/list?depCityCode=BJS&arrCityCode=HGH&depDate=2022-05-07&ch=111_{%22pre%22:%22CS%22,%22uid%22:9426681,%22device%22:%22fb4204dbc5dad3a65c38a29aa2a2e8b1%22,%22return%22:0}&from_d=10000&apos",
    "toubao_url": "",  # "http://fticketdev.feeyo.com/hzh/insgq?order_no=3507376771",
    "changetime_url": "",  # "http://fticketdev.feeyo.com/hzh/timeMachine?order_no=3507890308&days=-5",
    "checksms_url": "",  # "http://fticketdev.feeyo.com/sjj/smstest?order_no=3508298380&debug=9",
    "pay_url": "",
    # "http://fticketdev.feeyo.com/tv2/Cashier/callBack?pay_no=201201521869313&pay_price=26500&type=3&status=10&nbr=201201521869313",
    "clear_url": "",  # "http://fticketdev.feeyo.com/hzh/clear?uid=15198266",
    "freshlowprice_url": "",  # "http://fticketdev.feeyo.com/hzh/x7?debug=9"
    "sign": True,
}

class TestUtil:

    def __init__(self, data):
        self.data = data
    def start(self):
        userInput = input(
            '请输入对应序号选择使用：\n1.复制测试环境订单\n2.投保操作\n3.更改订单及订单保险时间\n4.刷新后台mongo、客户端日志\n5.支付\n6.删除用户机票数据\n7.刷新低价航班\n8.查看订单短信\n9.ui自动化出票国内（beta）\n11.清空账号优惠券数据\n12.生成虚拟身份证和姓名\n')
        self.user_action(int(userInput))

    def user_action(self, num):
        if num == 1:
            self.copy_order()
        elif num == 2:
            self.toubao()
        elif num ==3:
            self.changetime()
        elif num == 4:
            self.refresh_logs()
        elif num == 5:
            self.pay()
        elif num == 6:
            self.cleardata()
        elif num == 7:
            self.freshlowprice()
        elif num == 8:
            self.checksmslist()
        elif num == 9:
            self.chupiao()
        elif num == 11:
            self.delCoupon()
        elif num == 12:
            self.generate_virtual_identity()
        else:
            print('暂无对应方法！！')

    def loopornot(self, url):
        self.data["sign"] = True
        while self.data["sign"] == True:
            req= request_data(url)
            print('返回内容：', req)
            result = input('是否重新刷新?\n1.重新刷新\n(enter退出重新选择)\n')
            # if isjson==1:
            #
            #     if (req.get('code')!=None) and req['code'] == 0 and req['data'] != []:
            #         print('刷新成功！',req)
            #     else:
            #         print('返回异常！', req)
            #     result = input('是否重新刷新?\n1.重新刷新\n(enter退出重新选择)\n')
            # elif isjson==2:
            #     print('返回内容：',req)
            #     result = input('是否重新刷新?\n1.重新刷新\n(enter退出重新选择)\n')
            # elif isjson==3:

            # print(result)
            if result == '1':
                self.data["sign"] = True
            else:
                self.data["sign"] = False
        self.data["sign"] = False

    def copy_order(self):
        """
        复制测试环境订单功能
        用户可输入一个或多个订单号，用英文逗号隔开
        """
        result = input('请输入订单号（多个订单号用英文逗号隔开）：').strip()

        if not result:
            print("❌ 订单号不能为空")
            return

        # 分割订单号并去除空格
        order_numbers = [order.strip() for order in result.split(',') if order.strip()]

        if not order_numbers:
            print("❌ 请输入有效的订单号")
            return

        print(f'\n📝 将要复制 {len(order_numbers)} 个订单：')
        for i, order_no in enumerate(order_numbers, 1):
            print(f'  {i}. {order_no}')


        # 复制每个订单
        # success_count = 0
        # failed_orders = []

        for order_no in order_numbers:
            try:
                print(f'\n🔄 正在复制订单: {order_no}')
                url = f"https://fticketdev.variflight.com/admin/xuwei/copyOrder?order_no={order_no}"

                # 发送请求（使用改进的请求函数处理SSL问题）
                req = self.request_data_with_ssl_fix(url)
                print(f'返回内容：{req}')

                # 简单判断是否成功（可根据实际返回格式调整）
            #     if isinstance(req, dict):
            #         if req.get('code') == 0 or 'success' in str(req).lower():
            #             print(f'✅ 订单 {order_no} 复制成功')
            #             success_count += 1
            #         elif 'erro' in req:
            #             print(f'❌ 订单 {order_no} 复制失败：{req["erro"]}')
            #             failed_orders.append(order_no)
            #         else:
            #             print(f'⚠️  订单 {order_no} 复制状态未知，请检查返回内容')
            #             failed_orders.append(order_no)
            #     elif isinstance(req, str) and 'success' in req.lower():
            #         print(f'✅ 订单 {order_no} 复制成功')
            #         success_count += 1
            #     else:
            #         print(f'⚠️  订单 {order_no} 复制可能失败，请检查返回内容')
            #         failed_orders.append(order_no)

            except Exception as e:
                print(f'❌ 订单 {order_no} 复制失败：{e}')
                # failed_orders.append(order_no)

        # 显示总结
        # print(f'\n📊 复制完成统计：')
        # print(f'✅ 成功：{success_count} 个')
        # print(f'❌ 失败：{len(failed_orders)} 个')

        # if failed_orders:
        #     print(f'失败的订单号：{", ".join(failed_orders)}')

    def request_data_with_ssl_fix(self, url):
        """
        改进的请求函数，处理SSL连接问题
        """
        import requests
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        import urllib3

        try:
            # 禁用SSL警告（仅用于测试环境）
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

            # 创建会话
            session = requests.Session()

            # 设置重试策略
            retry_strategy = Retry(
                total=3,
                backoff_factor=1,
                status_forcelist=[429, 500, 502, 503, 504],
            )

            adapter = HTTPAdapter(max_retries=retry_strategy)
            session.mount("http://", adapter)
            session.mount("https://", adapter)

            # 发送请求，禁用SSL验证（仅用于测试环境）
            response = session.get(
                url,
                timeout=30,
                verify=False,  # 禁用SSL证书验证
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            )

            # 处理响应
            content_type = response.headers.get('Content-Type', '')
            if 'application/json' in content_type:
                return response.json()
            else:
                return response.text

        except requests.exceptions.SSLError as e:
            print(f"⚠️  SSL连接错误：{e}")
            print("💡 提示：这可能是测试环境的SSL证书问题，已尝试绕过SSL验证")
            return {"erro": f"SSL错误: {e}"}
        except requests.exceptions.ConnectionError as e:
            print(f"⚠️  网络连接错误：{e}")
            print("💡 提示：请检查网络连接或VPN设置")
            return {"erro": f"连接错误: {e}"}
        except requests.exceptions.Timeout as e:
            print(f"⚠️  请求超时：{e}")
            return {"erro": f"超时错误: {e}"}
        except Exception as e:
            print(f"⚠️  其他错误：{e}")
            return {"erro": f"未知错误: {e}"}

    def qudao_fresh(self):

        result = input('请按顺序输入出发地三字码、到达地三字码、出发日期(格式：2011-01-01)、渠道，并用逗号隔开')
        content = result.split(',')
        url = ' https://fticketdev.variflight.com/admin/test/channelJava?depCityCode=' + content[0] + '&arrCityCode=' + content[
            1] + '&depDate=' + content[2] + '&channel=' + content[
                  3]
        self.loopornot(url)

    def toubao(self):
        result = input('请输入订单号\n')
        # content = result.split(',')
        url = "http://fticketdev.variflight.com/hzh/insgq?order_no=" + result
        self.loopornot(url)

    def changetime(self):
        result = input('请按顺序输入订单号和天数（例：3507890308,-5），并用逗号隔开\n')
        content = result.split(',')
        if len(content) != 2:
            print('输入格式错误，请按照格式：订单号,天数')
            return

        order_no = content[0].strip()
        try:
            days = float(content[1].strip())
        except ValueError:
            print('天数必须是数字')
            return

        # 调用数据库更新函数
        from database.dataconnect import update_order_time
        update_order_time(order_no, days)

    def refresh_logs(self):
        print('开始刷新客户端日志及mongo日志...')
        print('接口1: https://fticketdev.variflight.com/sapi/Loggermonitor/sync')
        print('接口2: https://fticketdev.variflight.com/sapi/timer/update_10')
        print('间隔时间: 2秒')
        print('按 Ctrl+C 可中断执行\n')

        urls = [
            "https://fticketdev.variflight.com/sapi/Loggermonitor/sync",
            "https://fticketdev.variflight.com/sapi/timer/update_10"
        ]

        try:
            print('请求中', end='', flush=True)
            dot_count = 0  # 记录当前点的数量
            while True:
                for i, url in enumerate(urls, 1):
                    try:
                        request_data(url)
                        # 控制点的数量，最多3个，达到3个后清除重新开始
                        if dot_count < 3:
                            print('.', end='', flush=True)
                            dot_count += 1
                        else:
                            # 清除当前行，使用足够的空格覆盖之前的内容，然后重新开始
                            print('\r' + ' ' * 20 + '\r请求中', end='', flush=True)
                            dot_count = 1
                            print('.', end='', flush=True)
                    except Exception:
                        # 异常时也要控制感叹号的数量
                        if dot_count < 3:
                            print('!', end='', flush=True)
                            dot_count += 1
                        else:
                            # 清除当前行，使用足够的空格覆盖之前的内容，然后重新开始
                            print('\r' + ' ' * 20 + '\r请求中', end='', flush=True)
                            dot_count = 1
                            print('!', end='', flush=True)

                    if i < len(urls):  # 不是最后一个接口时才等待
                        time.sleep(2)

                time.sleep(2)

        except KeyboardInterrupt:
            print('\n\n用户中断执行，已停止请求')
        except Exception as e:
            print(f'\n执行过程中出现异常: {e}')

    # def pay(self):
    #     result = input('请按顺序输入pay_no和金额（单位分），并用逗号隔开\n')
    #     content = result.split(',')
    #     url = "http://fticketdev.feeyo.com/tv2/Cashier/callBack?pay_no=" + content[0] + "&pay_price=" + content[1] + "&type=3&status=10&nbr=" + content[0]
    #     self.loopornot(url)
    def pay(self):
        result = input('请输入订单号\n')
        payno,price=searchdata(result,1)
        # print(payno,price)
        url = "http://fticketdev.variflight.com/tv2/Cashier/callBack?pay_no={}&pay_price={}&type=3&status=10&nbr={}".format(payno,price,payno)
        self.loopornot(url)
    def cleardata(self):
        result = input('请输入uid\n')
        print(f'\n⚠️  警告：即将删除uid={result}的所有机票相关数据')
        print('涉及的数据表包括：')
        tables = [
            'tv2_consult_order', 'issue', 'user_debt_records', 'tv2_grab_order', 
            'blind_box_order', 'sm_order', 'sm_order_detail', 'na_order_detail', 
            'na_supplier_order', 'tv2_order_detail', 'tv2_order', 'tv2_supplier_order', 
            'tv2_order_extend', 'tv2_order_deduction', 'tv2_pay_bill', 
            'tv2_order_pay_summary', 'tv2_pay_serial', 'tv2_pay_marking_order', 
            'tv2_package', 'tv2_package_details', 'tv2_insurance', 'tv2_segment', 
            'tv2_refund_order'
        ]
        
        for i, table in enumerate(tables, 1):
            print(f'{i:2d}. {table}')
        
        confirm = input(f'\n确认删除uid={result}的所有机票数据吗？(回车确认，输入任意字符取消): ')
        if confirm != '':
            print('操作已取消')
            return
            
        try:
            # 调用批量删除函数
            searchdata(result, 4)
            print(f'\n✅ 用户uid={result}的机票数据清理完成')
        except Exception as e:
            print(f'❌ 删除过程中出现错误: {e}')
            print('请检查数据库连接和权限')

    def freshlowprice(self):
        url = "http://fticketdev.variflight.com/hzh/x7?debug=9"
        self.loopornot(url)
    def checksmslist(self):
        result = input('请输入订单号\n')
        searchdata(result,2)
    def chupiao(self):
        """
        自动出票功能 - 支持cookie管理的智能出票
        Cookie文件保存在桌面，方便用户管理
        """
        print("\n🎫 自动出票系统")
        print("="*50)
        result = input('请输入订单号: ').strip()

        if not result:
            print("❌ 订单号不能为空")
            return

        # 创建自动化实例
        auto_ticket = autoIssueTicketPlaywright(result)

        # 检查是否已有有效的cookie
        if auto_ticket.has_valid_cookies():
            print(f"\n✅ 检测到已保存的cookie ({len(auto_ticket.cookies)}个)")
            print("🚀 自动使用已保存的cookie，开始执行出票...")
        else:
            # 没有有效cookie，显示详细信息并需要输入
            print("\n⚠️  未检测到有效的cookie")

            # 显示cookie文件信息

            auto_ticket.input_cookies_from_string()
          

        # 开始自动化流程
        print(f"\n🚀 开始执行订单 {result} 的自动出票...")
        try:
            auto_ticket.autostart()
            print('\n✅ 自动出票执行完成！')
            print('📁 Cookie已保存到桌面，下次使用更便捷')
        except Exception as e:
            print(f'\n❌ 自动出票过程中出现错误: {e}')
            print('💡 提示：如果是cookie问题，请尝试重新输入cookie')
    def delCoupon(self):
        result = input('请输入uid\n')
        searchdata(result, 3)

    def generate_virtual_identity(self):
        """生成虚拟身份证和姓名"""
        print('\n===== 虚拟身份证和姓名生成器 =====')
        print('将生成10条虚拟身份信息')

        age_input = input('请输入指定年龄(直接回车则随机生成1-200岁的年龄)：')

        try:
            if age_input.strip():
                age = int(age_input)
                if age < 1 or age > 200:
                    print('年龄超出合理范围(1-200)，将使用随机年龄')
                    age = None
                else:
                    print(f'将生成{age}岁的虚拟身份信息')
            else:
                age = None
                print('将生成随机年龄(1-200岁)的虚拟身份信息')

            # 生成10条虚拟身份信息
            identities = IDGenerator.generate_identities(count=10, age=age)

            # 打印结果
            print('\n' + '='*60)
            print('{:<12}{:<8}{:<6}{:<20}'.format('姓名', '性别', '年龄', '身份证号'))
            print('-'*60)

            for identity in identities:
                print('{:<12}{:<8}{:<6}{:<20}'.format(
                    identity['姓名'],
                    identity['性别'],
                    identity['年龄'],
                    identity['身份证号']
                ))
            print('='*60 + '\n')

            # 询问是否需要重新生成
            while True:
                choice = input('是否需要重新生成？(y/n): ').lower()
                if choice == 'y':
                    self.generate_virtual_identity()
                    break
                elif choice == 'n':
                    break
                else:
                    print('无效的输入，请输入 y 或 n')

        except ValueError:
            print('输入的年龄格式不正确，请输入数字')
            self.generate_virtual_identity()
def main(data):
    # print(data['qudao_url'], 'bb')

    try:
        test = TestUtil(data)
        test.start()
        main1(data)
    except IndexError as e:
        print("请输入完整的参数！\n")
        main1(data)
    except ValueError as e:
        print('请输入数字进行选择！\n')
        main1(data)
    # except requests.exceptions.JSONDecodeError as e:
    #     print('接口异常！\n')
    #     main1(data)
    except Exception as e:
        print(e)
        main1(data)
    finally:
        pass


def main1(data):
    # print(data['qudao_url'], 'bb')

    try:
        test = TestUtil(data)
        test.start()
        main(data)
    except IndexError as e:
        print("请输入完整的参数！\n")
        main(data)
    except ValueError as e:
        print('请输入数字进行选择！\n')
        main(data)
    except Exception as e:
        print(e)
        main(data)
    finally:
        pass


if __name__ == '__main__':
    main(data)
    # test = TestUtil(data)
    # test.start()
