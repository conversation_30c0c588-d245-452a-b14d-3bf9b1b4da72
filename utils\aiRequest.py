import json
import time
import csv
import urllib.request
import urllib.parse
from urllib.error import HTTPError, URLError

class QuestionProcessor:
    
    def __init__(self):
        self.clear_url = "http://***************:8080/agent/history/clear"
        self.question_url = "http://***************:8080/agent/sse/ticket_agent"
        self.uid = "60794899"
        self.timeout = 60
    
    def process_questions(self, input_file, output_file):
        try:
            # 读取问题文件
            with open(input_file, 'r', encoding='utf-8') as f:
                questions = [line.strip() for line in f.readlines()]
            
            # 创建CSV输出文件
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                # 写入CSV头部
                writer.writerow(['question', 'answer'])
                
                # 处理每个问题
                for i, question in enumerate(questions):
                    if not question:
                        continue
                    
                    print(f"处理第 {i + 1}/{len(questions)} 个问题: {question}")
                    
                    try:
                        # 1. 清空上下文
                        self.clear_context()
                        
                        # 2. 提问并获取答案
                        answer = self.ask_question(question)
                        
                        # 3. 写入CSV
                        writer.writerow([question, answer])
                        csvfile.flush()
                        
                        print(f"完成第 {i + 1} 个问题，答案长度: {len(answer)}")
                        
                        # 稍微延迟，避免请求过快
                        time.sleep(1)
                        
                    except Exception as e:
                        print(f"处理问题失败: {question}, 错误: {str(e)}")
                        # 记录失败的问题
                        writer.writerow([question, f"处理失败: {str(e)}"])
                        csvfile.flush()
            
            print("所有问题处理完成！")
            
        except IOError as e:
            print(f"文件操作失败: {str(e)}")
    
    def clear_context(self):
        data = json.dumps({"uid": self.uid}).encode('utf-8')
        
        req = urllib.request.Request(
            self.clear_url,
            data=data,
            headers={
                'Content-Type': 'application/json; charset=utf-8',
                'Content-Length': str(len(data))
            },
            method='POST'
        )
        
        try:
            with urllib.request.urlopen(req, timeout=self.timeout) as response:
                if response.getcode() != 200:
                    raise Exception(f"清空上下文失败: {response.getcode()}")
        except (HTTPError, URLError) as e:
            raise Exception(f"清空上下文请求失败: {str(e)}")
    
    def ask_question(self, question):
        # 转义问题中的双引号
        escaped_question = question.replace('"', '\\"')
        data = json.dumps({"question": escaped_question, "uid": self.uid}).encode('utf-8')
        
        req = urllib.request.Request(
            self.question_url,
            data=data,
            headers={
                'Content-Type': 'application/json; charset=utf-8',
                'Content-Length': str(len(data))
            },
            method='POST'
        )
        
        try:
            with urllib.request.urlopen(req, timeout=self.timeout) as response:
                if response.getcode() != 200:
                    raise Exception(f"提问失败: {response.getcode()}")
                
                answer = []
                
                # 逐行读取SSE响应
                for line in response:
                    line = line.decode('utf-8').strip()
                    if line.startswith("data:"):
                        json_data = line[5:]  # 去掉 "data:" 前缀
                        try:
                            data_obj = json.loads(json_data)
                            if data_obj.get("type") == "MAIN_BODY":
                                msg = data_obj.get("msg", "")
                                answer.append(msg)
                        except json.JSONDecodeError:
                            # 忽略JSON解析错误，继续处理下一行
                            print(f"JSON解析错误: {json_data}")
                            continue
                
                return ''.join(answer)
                
        except (HTTPError, URLError) as e:
            raise Exception(f"提问请求失败: {str(e)}")

def main():
    processor = QuestionProcessor()
    processor.process_questions("./question.txt", "./answers.csv")

if __name__ == "__main__":
    main()