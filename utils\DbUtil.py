import pymysql

class DBUtil(object):
    conn=None

    @classmethod
    def __getconn(cls,database):
        if cls.conn is None:
            cls.conn=pymysql.connect(
            host="**************",  # 数据库主机地址
            user="toc_test",  # 数据库用户名
            passwd="toc_test@1644",  # 数据库密码
            port=8077,
            database=database
            )
        return cls.conn

    @classmethod
    def __closeconn(cls):
        if cls.conn is not None:
            cls.conn.close()
            cls.conn=None


    @classmethod
    def select_db(cls,sql,database):
        cursor=None
        res=None
        try:
            cls.conn=cls.__getconn(database)
            cursor=cls.conn.cursor()
            cursor.execute(sql)
            res=cursor.fetchall()
        except Exception as e:
            print('sql执行出错',str(e))
        finally:
            cursor.close()
            cls.__closeconn()
            return res
    @classmethod
    def adm_db(cls,sql,database):
        cursor = None
        try:
            cls.conn=cls.__getconn(database)
            cursor = cls.conn.cursor()
            cursor.execute(sql)
            print('影响的行数:',cls.conn.affected_rows())
            cls.conn.commit()
        except Exception as e:
            print('sql执行出错', str(e))
            cls.conn.rollback()
        finally:
            # print(cursor)
            cursor.close()
            cls.__closeconn()

    @classmethod
    def del_db(cls,sql,database):
        cursor = None
        try:
            cls.conn=cls.__getconn(database)
            cursor = cls.conn.cursor()
            cursor.execute(sql)
            print('删除成功，影响的行数:',cls.conn.affected_rows())
            cls.conn.commit()
        except Exception as e:
            print('sql执行出错', str(e))
            cls.conn.rollback()
        finally:
            # print(cursor)
            cursor.close()
            cls.__closeconn()
if __name__ == '__main__':
    pass
    # res=DBUtil.select_db('select * from flight_sms where foid=3606809730')
    # # DBUtil.adm_db("UPDATE tv2_ticket_rule SET change_fomula='40-168-60-72-80-4-90',refund_fomula='0-4-90' where id in (40504,40505)")
    # print(res)