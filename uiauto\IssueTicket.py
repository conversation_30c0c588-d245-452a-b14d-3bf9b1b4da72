# NOTE: To use <PERSON>wright, install it with: pip install playwright && playwright install
# 导包
from selenium import webdriver
from time import sleep
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from playwright.sync_api import sync_playwright

class autoIssueTicket:
    def __init__(self,orderno):
        self.orderno=orderno
        # self.driver=webdriver.Chrome(ChromeDriverManager().install())
        self.driver=webdriver.Chrome(executable_path='C:\Program Files\Google\Chrome\Application\chromedriver.exe')
    def screenshot(self):

        self.driver.get_screenshot_as_file('erro.png')
    def autostart(self):
        self.driver.get('http://fticketdev.variflight.com/manage/Login/index')
        self.driver.maximize_window()
        self.driver.implicitly_wait(1)
        self.driver.find_element(By.ID, 'LAY-user-login-username').send_keys('wangwei')
        self.driver.find_element(By.ID, 'LAY-user-login-password').send_keys('Ww,.123456')
        self.driver.implicitly_wait(1)
        self.driver.find_element(By.ID, 'login-submit').click()
        self.driver.implicitly_wait(1)
        self.driver.find_element(By.PARTIAL_LINK_TEXT, '客服中心').click()
        self.driver.implicitly_wait(1)
        self.driver.find_element(By.PARTIAL_LINK_TEXT, '出票单查询').click()
        self.driver.implicitly_wait(1)
        self.driver.switch_to.frame(2)  # 出票页菜单为多个iframe需要注意切换
        self.driver.implicitly_wait(1)
        self.driver.find_element(By.XPATH, "//input[@name='order_no']").send_keys(self.orderno)
        self.driver.find_element(By.ID, 'LAY-form-search').click()
        self.driver.implicitly_wait(1)
        # sleep(2)
        suodan = self.driver.find_elements(By.PARTIAL_LINK_TEXT, '锁单')
        if suodan == []:
            self.driver.implicitly_wait(1)
            # sleep(2)
            self.driver.find_element(By.XPATH, '/html/body/div[1]/div/div[2]/ul/li[2]').click()
            sleep(1)
            self.driver.find_element(By.PARTIAL_LINK_TEXT, '审单').click()
            # return 1
            sleep(1)
            self.driver.switch_to.frame(0)
            # sleep(100)
            a = self.driver.find_elements(By.PARTIAL_LINK_TEXT, '审核出票')
        else:
            suodan[0].click()
            self.driver.implicitly_wait(1)
            # sleep(2)
            self.driver.find_element(By.PARTIAL_LINK_TEXT, '审单').click()
            # return 1
            sleep(1)
            self.driver.switch_to.frame(0)
            # sleep(100)
            a = self.driver.find_elements(By.PARTIAL_LINK_TEXT, '审核出票')

        num = [777]
        for index, yuansu in enumerate(a):
            self.driver.implicitly_wait(1)
            aa = self.driver.find_elements(By.PARTIAL_LINK_TEXT, '审核出票')
            aa[0].click()
            self.driver.switch_to.default_content()  # 退出
            self.driver.switch_to.frame(3)
            b = self.driver.find_elements(By.NAME, 'ticket_no[]')
            # print(b)
            for i, y in enumerate(b):
                y.send_keys(str(num[0]) + '-' + self.orderno)
                num[0] += 1

            sleep(1)
            self.driver.find_element(By.XPATH, '/html/body/div[1]/div/div/form/div[6]/button[1]').click()

            self.driver.implicitly_wait(2)
            self.driver.find_element(By.PARTIAL_LINK_TEXT, '确定').click()
            sleep(4)

            if index != (len(a) - 1):
                self.driver.switch_to.default_content()
                self.driver.find_element(By.PARTIAL_LINK_TEXT, '出票单查询').click()
                self.driver.switch_to.frame(2)
                self.driver.find_element(By.PARTIAL_LINK_TEXT, '审单').click()
                sleep(1)
                self.driver.switch_to.frame(0)
        self.driver.implicitly_wait(1)
        self.driver.switch_to.default_content()  # 退出

        self.driver.quit()

class autoIssueTicketPlaywright:
    def __init__(self, orderno):
        self.orderno = orderno
        # 将cookie文件保存到桌面
        import os
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        self.cookie_file = os.path.join(desktop_path, "自动出票_cookies.txt")
        # 初始化空的cookie数据
        self.cookies = []
        # 尝试加载已保存的cookie
        self.load_saved_cookies()

    def update_cookies(self, new_cookies_dict):
        """
        更新cookie数据
        参数: new_cookies_dict - 字典格式的cookie数据
        例如: {"PHPSESSID": "new_session_id", "rock_format": "json"}
        """
        for cookie in self.cookies:
            if cookie["name"] in new_cookies_dict:
                cookie["value"] = new_cookies_dict[cookie["name"]]
                print(f"已更新cookie: {cookie['name']} = {cookie['value']}")

    def add_cookie(self, name, value, domain="fticketdev.variflight.com", path="/"):
        """
        添加新的cookie
        """
        new_cookie = {"name": name, "value": value, "domain": domain, "path": path}
        self.cookies.append(new_cookie)
        print(f"已添加新cookie: {name} = {value}")

    def save_cookies_to_file(self):
        """
        将cookie保存到桌面文件
        """
        try:
            import json
            import os

            # 确保桌面目录存在
            desktop_dir = os.path.dirname(self.cookie_file)
            if not os.path.exists(desktop_dir):
                os.makedirs(desktop_dir)

            # 保存cookie数据
            with open(self.cookie_file, 'w', encoding='utf-8') as f:
                json.dump(self.cookies, f, ensure_ascii=False, indent=2)

            print(f"✓ Cookie已保存到桌面: {os.path.basename(self.cookie_file)}")
            print(f"  完整路径: {self.cookie_file}")
            print(f"  您可以在桌面找到此文件进行查看、编辑或删除")
        except Exception as e:
            print(f"❌ 保存cookie失败: {e}")
            print(f"  尝试保存到: {self.cookie_file}")

    def load_saved_cookies(self):
        """
        从桌面文件加载已保存的cookie
        """
        try:
            import json
            import os
            if os.path.exists(self.cookie_file):
                with open(self.cookie_file, 'r', encoding='utf-8') as f:
                    self.cookies = json.load(f)
                print(f"✓ 已从桌面加载cookie ({len(self.cookies)}个)")
                print(f"  文件位置: {os.path.basename(self.cookie_file)}")
                return True
            else:
                print(f"📁 未找到cookie文件: {os.path.basename(self.cookie_file)}")
                print(f"   将在桌面创建新的cookie文件")
                return False
        except Exception as e:
            print(f"❌ 加载cookie失败: {e}")
            print(f"   文件路径: {self.cookie_file}")
            return False

    def has_valid_cookies(self):
        """
        检查是否有有效的cookie
        """
        return len(self.cookies) > 0 and any(cookie.get("value") for cookie in self.cookies)

    def input_cookies_from_string(self):
        """
        从cookie字符串解析并赋值给现有cookie系统
        支持浏览器复制的cookie格式
        """
        print("请输入cookie字符串:")
        print("格式示例：PHPSESSID=abc123; _ga=GA1.1.123456; HMACCOUNT=4FB2488201D8AB78")
        print("-" * 50)

        cookie_string = input("请粘贴cookie字符串: ").strip()
        if not cookie_string:
            print("cookie字符串为空")
            return

        # 清空现有cookie
        self.cookies = []

        # 解析cookie字符串
        cookie_pairs = cookie_string.split(';')
        for pair in cookie_pairs:
            if '=' in pair:
                name, value = pair.strip().split('=', 1)
                name = name.strip()
                value = value.strip()

                if name and value:
                    # 自动判断域名
                    if name == "PHPSESSID":
                        domain = "fticketdev.variflight.com"
                    else:
                        domain = ".variflight.com"

                    self.cookies.append({
                        "name": name,
                        "value": value,
                        "domain": domain,
                        "path": "/"
                    })
                    print(f"✓ 已解析cookie: {name}")

        print(f"\n总共解析了 {len(self.cookies)} 个cookie")

        # 自动保存cookie到文件
        if self.cookies:
            self.save_cookies_to_file()

    def find_browser_path(self):
        """
        智能检测系统中可用的浏览器路径
        """
        import os

        # 常见的浏览器路径
        browser_paths = [
            # Chrome路径
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            # # Edge路径
            # r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
            # r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
            # 其他可能的Chrome路径
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe"),
        ]

        for path in browser_paths:
            if os.path.exists(path):
                return path

        # print("⚠️  未找到Chrome或Edge浏览器")
        # print("请安装以下浏览器之一：")
        print("⚠️  未找到Chrome浏览器")
        print("• Google Chrome")
        # print("• Microsoft Edge")
        return None

    def autostart(self):

        with sync_playwright() as p:
            # 智能检测浏览器路径
            browser_path = self.find_browser_path()
            if browser_path:
                print(f"🌐 使用浏览器: {browser_path}")
                browser = p.chromium.launch(
                    headless=False,
                    executable_path=browser_path
                )
            else:
                print("❌ 未找到可用的浏览器，请安装Chrome")
                return
            context = browser.new_context()
            page = context.new_page()

            # 先访问目标域名以设置cookie
            page.goto('https://fticketdev.variflight.com')

            # 添加cookie到浏览器上下文
            try:
                # 过滤掉空值的cookie
                valid_cookies = [cookie for cookie in self.cookies if cookie.get("value")]
                context.add_cookies(valid_cookies)
                print(f"已添加{len(valid_cookies)}个有效cookie到浏览器上下文")
            except Exception as e:
                print(f"添加cookie时出现错误: {e}")
                print("将继续使用手动登录方式")
            # 1. 使用cookie直接访问后台页面（跳过登录）
            try:
                page.goto('https://fticketdev.variflight.com/manage/Index/index?tab_title=%E9%A3%9E%E5%B8%B8%E5%87%86%E6%9C%BA%E7%A5%A8%E5%90%8E%E5%8F%B0v2.0&tab_href=%2Fmanage%2Fhome%2Fdesktop')
                page.wait_for_timeout(2000)

                # 检查是否成功登录（如果页面包含登录表单，说明cookie失效）
                if page.query_selector('#LAY-user-login-username'):
                    print("Cookie可能已失效，尝试手动登录...")
                    page.fill('#LAY-user-login-username', 'wangwei')
                    page.fill('#LAY-user-login-password', 'Ww,.123456')
                    page.click('#login-submit')
                    page.wait_for_timeout(2000)
                else:
                    print("使用cookie成功登录！")
            except Exception as e:
                print(f"登录过程出现错误: {e}")
                # 如果出错，尝试直接登录
                page.goto('https://fticketdev.variflight.com/manage/Login/index')
                page.fill('#LAY-user-login-username', 'wangwei')
                page.fill('#LAY-user-login-password', 'Ww,.123456')
                page.click('#login-submit')
                page.wait_for_timeout(2000)

            # 查询数据库判断flight_way字段值
            flight_way = self.check_flight_way()
            if flight_way in [7, 9]:
                print(f"✈️ 检测到flight_way={flight_way}，执行流程A（国际出票）")
                # self.execute_process_a(page)
            else:
                print(f"🛫 检测到flight_way={flight_way}，执行流程B（国内出票）")
                # self.execute_process_b(page)

    def check_flight_way(self):
        """
        查询数据库获取订单的flight_way字段值
        """
        try:
            from database.dataconnect import DBUtil
            sql = f"SELECT flight_way FROM tv2_order WHERE order_no = '{self.orderno}'"
            result = DBUtil.select_db(sql, 'cdev')

            if result and len(result) > 0:
                flight_way = result[0][0]  # 获取第一行第一列的值
                return flight_way
            else:
                print(f"⚠️  未找到订单 {self.orderno} 的信息，使用默认流程")
                return 1  # 默认值，执行国内流程
        except Exception as e:
            print(f"❌ 查询数据库失败: {e}")
            print("🔄 使用默认流程")
            return 1  # 出错时默认执行国内流程

    def execute_process_a(self, page):
        """
        执行流程A - 国际出票流程 (flight_way = 7 或 9)
        """

        page.wait_for_timeout(1000)
        # TODO: 在这里实现国际出票的具体逻辑
        # 暂时使用国内流程作为占位符

    def execute_process_b(self, page):
        """
        执行流程B - 国内出票流程 (其他flight_way值)
        """

        page.wait_for_timeout(1000)
            # 2. 进入客服中心 -> 出票单查询
        page.click('text=客服中心')
        page.wait_for_timeout(1000)
        page.click('text=出票单查询')
        page.wait_for_timeout(1000)
        # 3. 切换到iframe
        frames = page.frames
        if len(frames) < 3:
            page.wait_for_timeout(2000)
            frames = page.frames
        ticket_frame = frames[2]  # 第3个iframe
        ticket_frame.fill('input[name="order_no"]', self.orderno)
        ticket_frame.click('#LAY-form-search')
        page.wait_for_timeout(1000)
        # 4. 锁单判断
        suodan = ticket_frame.query_selector_all('text=锁单')
        if not suodan:
            ticket_frame.click('xpath=/html/body/div[1]/div/div[2]/ul/li[2]')
            page.wait_for_timeout(1000)
            ticket_frame.click('text=审单')
            page.wait_for_timeout(1000)
            audit_frame = page.frames[0]
        else:
            suodan[0].click()
            page.wait_for_timeout(1000)
            ticket_frame.click('text=审单')
            page.wait_for_timeout(1000)
            audit_frame = page.frames[0]
        # 5. 审核出票
        a = audit_frame.query_selector_all('text=审核出票')
        num = 777
        for index, _ in enumerate(a):
            audit_frame.click('text=审核出票')
            page.wait_for_timeout(500)
            page.main_frame.wait_for_timeout(500)
            page.main_frame.goto(page.url)  # 保证frame刷新
            # 切换到出票frame
            out_frames = page.frames
            if len(out_frames) < 4:
                page.wait_for_timeout(1000)
                out_frames = page.frames
            out_frame = out_frames[3]
            ticket_inputs = out_frame.query_selector_all('input[name="ticket_no[]"]')
            for y in ticket_inputs:
                y.fill(f'{num}-{self.orderno}')
                num += 1
            out_frame.click('xpath=/html/body/div[1]/div/div/form/div[6]/button[1]')
            page.wait_for_timeout(2000)
            out_frame.click('text=确定')
            page.wait_for_timeout(4000)
            if index != (len(a) - 1):
                page.main_frame.click('text=出票单查询')
                ticket_frame = page.frames[2]
                ticket_frame.click('text=审单')
                page.wait_for_timeout(1000)
                audit_frame = page.frames[0]
        browser.close()

# 使用示例
if __name__ == "__main__":
    # 使用Playwright版本（带cookie）
    order_no = input("请输入订单号: ").strip() or "3606497144"

    # 创建自动化实例
    auto_ticket = autoIssueTicketPlaywright(order_no)

    # 检查是否已有有效的cookie
    if auto_ticket.has_valid_cookies():
        print(f"✓ 检测到已保存的cookie ({len(auto_ticket.cookies)}个)")
        use_saved = input("是否使用已保存的cookie？(y/n，默认y): ").strip().lower()

        if use_saved in ['n', 'no']:
            # 用户选择重新输入cookie
            print("\n请重新输入cookie:")
            auto_ticket.input_cookies_from_string()
        else:
            print("使用已保存的cookie")
    else:
        # 没有有效cookie，需要输入
        print("\n未检测到有效的cookie，请输入:")
        print("选择cookie输入方式:")
        print("1. 从浏览器复制的cookie字符串导入")
        print("2. 不使用cookie（直接登录）")

        choice = input("请选择 (1/2): ").strip()

        if choice == "1":
            # 从cookie字符串导入
            auto_ticket.input_cookies_from_string()
        else:
            print("将使用直接登录方式")

    # 开始自动化流程
    try:
        auto_ticket.autostart()
        print("自动化流程完成！")
    except Exception as e:
        print(f"自动化过程中出现错误: {e}")
