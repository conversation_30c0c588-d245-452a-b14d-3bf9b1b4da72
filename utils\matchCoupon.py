import json
import requests

# 定义两个接口的URL
url1 = "http://fticketdev.feeyo.com/tv2/Coupontest/list?uid=15198266&pid=3"
url2 = "http://openapi.variflight.com/sapi/coupon/list?timestamp=1678870816&token=123&uid=15198266&pid=3&unique_id=&debug=1"

# 发送请求并获取JSON数据
response1 = requests.get(url1)
response2 = requests.get(url2)

# 将JSON数据转换为Python对象
api1_data = response1.json()
api2_data = response2.json()


def match(api2_data, api1_data):
    # 比较两个Python对象是否相等
    if api1_data == api2_data:
        print("两个接口的返回字段和值完全相等")
    else:
        # # 获取在第一个Python对象中存在但在第二个Python对象中不存在的键
        # diff_keys1 = set(api1_data.keys()) - set(api2_data.keys())
        #
        # # 获取在第二个Python对象中存在但在第一个Python对象中不存在的键
        # diff_keys2 = set(api2_data.keys()) - set(api1_data.keys())

        # 获取在两个Python对象中都存在但值不相等的键
        diff1_values = []
        diff2_values = []
        diff_keys = []
        for key in set(api1_data.keys()) & set(api2_data.keys()):
            print('php键{}：{},类型为{}\n'.format(key,api2_data[key],type(api2_data[key])),'java键{}：{},类型为{}'.format(key,api1_data[key],type(api1_data[key])))
            if api1_data[key] != api2_data[key]:
                diff_keys.append(key)
                diff1_values.append(api1_data[key])
                diff2_values.append(api2_data[key])
        # 输出差异信息
        # if diff_keys1:
        #     print("第一个接口返回的数据比第二个接口多出以下字段：{}".format(diff_keys1))
        # if diff_keys2:
        #     print("第二个接口返回的数据比第一个接口多出以下字段：{}".format(diff_keys2))
        if diff_keys:
            print("以下字段在两个接口的返回数据中存在但值不相等：{}\n具体的值为：({},{})\n".format(diff_keys,diff1_values,diff2_values))
        else:
            print('不存在不相等的字段及值\n')
# print(api2_data['data'][0]['coupon_sn'])
for i in api2_data['data']:
    for j in api1_data['data']:
        if i['coupon_sn']==j['coupon_sn']:
          match(i,j)
    #       break
    # else:
    #     continue
    # break
